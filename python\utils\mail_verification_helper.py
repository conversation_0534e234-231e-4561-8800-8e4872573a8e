import logging
import aiohttp
import asyncio
import re

class MailVerificationHelper:
    """验证码获取工具类"""
    api_key = None

    @classmethod
    def init(cls, api_key: str):
        cls.api_key = api_key
    
    @classmethod
    async def get_ms_verification_code(cls, proof_api: str, proof_email: str, timestamp: int) -> str:
        """获取验证码"""
        if not cls.api_key:
            raise Exception("API key not initialized")
        
        timestamp = timestamp - 5
        headers = {
            'Authorization': f'Bearer {cls.api_key}'
        }
        params = {
            'to': proof_email,
            'from': '<EMAIL>',
            'timestamp': timestamp
        }

        print(f"Getting verification code from {timestamp}")
        
        async with aiohttp.ClientSession() as session:
            max_retries = 30
            for _ in range(max_retries):
                async with session.get(proof_api +"/latest-email", headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        match = re.search(r':\s*(\d+)\n\n', data['text'])
                        if match:
                            print(f"Got verification code: {match.group(1)}")
                            return match.group(1)
                    else:
                        print(await response.text())
                await asyncio.sleep(3)
            raise Exception("Failed to get verification code after maximum retries")

    @classmethod
    async def get_kluster_verification_code(cls, proof_api: str, proof_email: str, timestamp: int) -> str:
        """获取验证链接"""
        if not cls.api_key:
            raise Exception("API key not initialized")
            
        headers = {
            'Authorization': f'Bearer {cls.api_key}'
        }
        params = {
            'to': proof_email,
            'from': 'kluster.ai',
            'timestamp': timestamp,
            'fuzzy': 'true'
        }

        print(f"Getting verification link from {timestamp}, api: {proof_api}, email: {proof_email}, key: {cls.api_key}")
        
        async with aiohttp.ClientSession() as session:
            max_retries = 30
            for _ in range(max_retries):
                async with session.get(proof_api+"/find-emails", headers=headers, params=params) as response:
                    if response.status == 200:
                        mails = await response.json()
                        for mail in mails:
                            pattern = r"https://platform\.kluster\.ai/email/([\w-]+)(?=\]|\s|$)"
                            urls = re.findall(pattern, mail['text'])
                            if urls:
                                return urls[0]
                    else:
                        print(await response.text())    
                    
                await asyncio.sleep(2)
            raise Exception("Failed to get verification link after maximum retries")
