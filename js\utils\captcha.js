

const captchaRunToken = "bf547423-dab7-49c0-b705-a046b2d40519";
const twoCapchaKey = "0c297cfb9102ac4ed945ba2ffdd04969"

/**
 * Get a token from 2captcha service (v2 implementation using fetch API)
 * @param {string} siteKey - The captcha site key
 * @param {string} siteReferer - The site referer URL
 * @param {string} siteAction - The site action
 * @param {number} maxRetries - Maximum number of retries, default 30
 * @param {number} retryInterval - Interval between retries in milliseconds, default 5000
 * @param {string} userAgent - Browser user agent
 * @returns {Promise<string>} - The captcha token
 */
export async function getTwoCaptchaTokenV2(siteKey, siteReferer, siteAction, maxRetries = 30, retryInterval = 5000, userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36") {
    try {
        // Step 1: Create captcha task
        const createTaskResponse = await fetch('https://api.2captcha.com/createTask', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                clientKey: twoCapchaKey,
                task: {
                    type: 'RecaptchaV3TaskProxyless',
                    websiteURL: siteReferer,
                    websiteKey: siteKey,
                    minScore: 0.9,
                    pageAction: siteAction,
                    isEnterprise: false
                },
            })
        });

        const createTaskResult = await createTaskResponse.json();

        if (createTaskResult.errorId !== 0) {
            throw new Error(`Failed to create task: ${JSON.stringify(createTaskResult)}`);
        }

        const taskId = createTaskResult.taskId;

        // Step 2: Get task result (with retry logic)
        let retries = 0;

        while (retries < maxRetries) {
            // Wait for the specified interval
            await new Promise(resolve => setTimeout(resolve, retryInterval));

            const getResultResponse = await fetch('https://api.2captcha.com/getTaskResult', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientKey: twoCapchaKey,
                    taskId: taskId,
                })
            });

            const getResultData = await getResultResponse.json();

            if (getResultData.errorId !== 0) {
                throw new Error(`Failed to get task result: ${JSON.stringify(getResultData)}`);
            }

            // Check if task is completed
            if (getResultData.status === 'ready') {
                console.log(`Recognition successful: ${JSON.stringify(getResultData)}`);
                // Return the captcha token
                return getResultData.solution.token;
            }

            // If task is still processing, increment retry count
            retries++;
        }

        throw new Error(`Exceeded maximum retries (${maxRetries}), unable to get captcha result`);
    } catch (error) {
        throw new Error(`reCAPTCHA v3 solution failed: ${error.message}`);
    }
}

/**
 * Get a token from captcha.run service
 * @param {string} captchaRunToken - The captcha.run API token
 * @param {string} siteKey - The captcha site key
 * @param {string} siteReferer - The site referer URL
 * @param {string} siteAction - The site action
 * @param {number} timeout - Timeout in seconds, default 180
 * @param {number} interval - Polling interval in seconds, default 3
 * @returns {Promise<string>} - The captcha token
 */
export async function getCaptchaRunToken(siteKey, siteReferer, siteAction, timeout = 180, interval = 3) {
    const headers = {
        'Authorization': `Bearer ${captchaRunToken}`,
        'Content-Type': 'application/json'
    };

    // Create task
    const response = await fetch("https://api.captcha.run/v2/tasks", {
        method: "POST",
        headers: headers,
        body: JSON.stringify({
            captchaType: "ReCaptchaV3",
            siteReferer: siteReferer,
            siteKey: siteKey,
            siteAction: siteAction
        })
    });

    if (!response.ok) {
        throw new Error(`Failed to create task: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    const taskId = result.taskId;

    if (!taskId) {
        throw new Error("Failed to create task");
    }

    console.log(`Created task: ${taskId}`);

    // Poll for results
    const startTime = Date.now();
    while ((Date.now() - startTime) / 1000 < timeout) {
        try {
            const statusResponse = await fetch(`https://api.captcha.run/v2/tasks/${taskId}`, {
                method: "GET",
                headers: headers
            });

            if (!statusResponse.ok) {
                throw new Error(`Failed to get task status: ${statusResponse.status} ${statusResponse.statusText}`);
            }

            const statusResult = await statusResponse.json();
            const solution = statusResult.response || {};

            if (statusResult.status === "Fail") {
                throw new Error("Recognition failed");
            }

            if (solution && solution.gRecaptchaResponse) {
                console.log(`Recognition successful: ${JSON.stringify(solution)}`);
                return solution.gRecaptchaResponse;
            }

            // Wait before polling again
            await new Promise(resolve => setTimeout(resolve, interval * 1000));
        } catch (error) {
            console.error(`Error getting task result: ${error.message}`);
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, interval * 1000));
        }
    }

    throw new Error("Captcha recognition timed out");
}
