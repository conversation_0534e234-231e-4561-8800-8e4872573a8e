import logging
import asyncio
import random
import signal
import sys
from playwright.async_api import <PERSON><PERSON>, Page
from utils.config_manager import ConfigManager
import time
from utils.mail_verification_helper import MailVerificationHelper

class MicrosoftDiedCheckTask:
    """
    Microsoft 账号死号检查任务
    """
    def __init__(
        self,
        config_manager: ConfigManager
    ) -> None:
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager

    async def handler_single_account(self, page: Page, email: str, password: str, proof_email:str,index: int = 0, max_retries: int = 3):
        """执行死号检查任务"""
        retry_count = 0
        while retry_count < max_retries:
            try:
                self.logger.info(f"开始检查账号 [{index}]: {email} (尝试 {retry_count + 1})")
                
                # 清除所有cookies
                await page.context.clear_cookies()

                # 访问奖励页面
                await page.goto("https://rewards.bing.com")
                # 输入邮箱
                await page.wait_for_url('https://login.live.com/oauth20_authorize.srf**', timeout=3000)
                await page.fill('input[type="email"]', email)
                await page.click('button[type="submit"]')
      

                try:
                    await page.wait_for_selector('#idA_PWD_SwitchToPassword', timeout=3000)
                    await page.click('#idA_PWD_SwitchToPassword')
                except Exception as e:
                    self.logger.info(f"没有切换密码登录，继续执行: {str(e)}")
          
                # 输入密码
                await page.fill('input[type="password"]', password)
                await page.click('button[type="submit"]')
                
                proof_domain = proof_email.split('@')[1]
                proof_email_config = None
                for config in self.config_manager.proof_email:
                    if config["domain"] == proof_domain:
                        proof_email_config = config
                        break
                proof_api = proof_email_config['api']
                
                try:
                    await page.wait_for_url(lambda url: url.startswith('https://account.live.com/recover'), timeout=5000)
                    await page.click('input[type="submit"]')    
                    
                    await page.wait_for_selector("#iProofEmail", timeout=3000)
                    await page.fill("#iProofEmail", proof_email)
                    timestamp = int(time.time())
                    await page.click('input[type="submit"]')
                    # 获取验证码
                    verification_code = await MailVerificationHelper.get_ms_verification_code(proof_api,proof_email, timestamp)
                    await page.fill('input[type="tel"]', verification_code)
                    await page.click('input[type="submit"]')
                except Exception as e:
                    self.logger.info(f"没有帮助我们保护帐户页面，继续执行: {str(e)}")
                
                # 处理多重验证
                for i in range(2):
                    try:
                        await page.wait_for_url('https://account.live.com/identity/**', timeout=3000)
                   
                        try:
                            await page.wait_for_selector("#iProof0", timeout=1000)
                            await page.click('#iProof0')
                        except Exception as e:
                            self.logger.info(f"没有选择email: {str(e)}")
                   
                        await page.fill("#iProofEmail", proof_email)
                        timestamp = int(time.time())
                        await page.click('input[type="submit"]')
                        # 获取验证码
                        verification_code = await MailVerificationHelper.get_ms_verification_code(proof_api,proof_email, timestamp)
                        await page.fill('input[type="tel"]', verification_code)
                        await page.click('input[type="submit"]')
                    except Exception as e:
                        self.logger.info(f"没有邮箱验证，继续执行: {str(e)}")
                
                # 确认登录
                try:
                    await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=3000)
                    await page.click('button[type="submit"]')
                except Exception as e:
                    self.logger.info(f"无登录确认，继续执行: {str(e)}")
                
                error_message = None
                try:
                    # 等待可能出现的错误提示
                    error_message = await page.wait_for_selector('#rewards-user-suspended-error', timeout=30000)
                except Exception as e:
                    self.logger.info(f"错误:{str(e)}")
          
                if error_message:
                    error_text = await (page.locator("#suspendedAccountHeader")).inner_text()
                    if "已暂停" in error_text:
                        retry_count = max_retries
                        raise Exception(error_text)
                break
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                await asyncio.sleep(random.uniform(5, 10))
                continue

    async def do(self, playwright: Playwright, accounts: list, max_concurrent_tasks: int = 1):
        failed_accounts = []
        active_accounts = []
        sem = asyncio.Semaphore(max_concurrent_tasks)
        tasks = []
        loop = asyncio.get_running_loop()

        def handle_sigint():
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        if sys.platform == 'win32':
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            loop.add_signal_handler(signal.SIGINT, handle_sigint)
        
        async def process_account_group(account_group):
            async with sem:
                return await self.batch(playwright, account_group)
        
        accounts_per_group = len(accounts) // max_concurrent_tasks
        remainder = len(accounts) % max_concurrent_tasks

        account_groups = []
        start_idx = 0

        for i in range(max_concurrent_tasks):
            group_size = accounts_per_group + (1 if i < remainder else 0)
            account_groups.append(accounts[start_idx:start_idx + group_size])
            start_idx += group_size
        
        for account_group in account_groups:
            task = asyncio.create_task(process_account_group(account_group))
            tasks.append(task)
    
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed: {str(result)}")
                elif isinstance(result, tuple):  # 修改为接收元组返回值
                    active, failed = result
                    active_accounts.extend(active)
                    failed_accounts.extend(failed)
                    
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)
    
        return active_accounts, failed_accounts  # 返回两个列表
            
    async def batch(self, playwright: Playwright, accounts: list):
        """批量处理检查任务"""
        active_accounts = []
        failed_accounts = []  # 新增失败账号列表

        browser_options = {
            'headless': False,
            'args': ['--disable-web-security']
        }
        
        context_options = {
            'ignore_https_errors': True
        }
        
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)
        page = await context.new_page()
        
        try:
            for index, account in enumerate(accounts, 1):
                try:
                    email = account['email']
                    password = account['password']
                    proof_email = account['proofEmail']
                    await self.handler_single_account(page, email, password, proof_email,index)
                    active_accounts.append(account)
                    await asyncio.sleep(random.uniform(3, 5))
                except Exception as e:
                    self.logger.error(f"Account checking failed for [{index}] {email}: {str(e)}")
                    failed_accounts.append(account)  # 添加到失败列表
                    continue
        finally:
            await page.close()
            await context.close()
            await browser.close()
            
        return active_accounts, failed_accounts  # 返回两个列表
