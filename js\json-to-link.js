/*
 * @description: This script converts a JSON file containing vmess/vless nodes into shareable links.
 *
 * @usage: node json-to-link.js <path/to/your/nodes.json>
 *
 * @example_input (nodes.json):
 * [
 *   {
 *     "name": "vmess-node-1",
 *     "type": "vmess",
 *     "server": "server.com",
 *     "port": 443,
 *     "uuid": "your-uuid",
 *     "alterId": "0",
 *     "cipher": "auto",
 *     "tls": true,
 *     "network": "ws",
 *     "ws-opts": { "path": "/path" }
 *   },
 *   {
 *     "name": "vless-node-1",
 *     "type": "vless",
 *     "server": "server.com",
 *     "port": 443,
 *     "uuid": "your-uuid",
 *     "tls": true,
 *     "network": "ws",
 *     "ws-opts": { "path": "/path" }
 *   }
 * ]
 *
 * @example_output:
 * vmess://...
 * vless://...
 */

// D:/Workspace/scripts/js/json-to-link.js

const fs = require('fs');
const path = require('path');

// --- Conversion Functions ---

function convertToVmess(node) {
    const vmessConfig = {
        v: "2",
        ps: node.name,
        add: node.server,
        port: node.port,
        id: node.uuid,
        aid: node.alterId,
        net: node.network,
        type: "none", // Default for ws/h2
        host: node.servername || (node['ws-opts'] && node['ws-opts'].headers && node['ws-opts'].headers.Host) || '',
        path: (node['ws-opts'] && node['ws-opts'].path) || '',
        tls: node.tls ? "tls" : "none"
    };

    // Some clients use 'scy' for security/cipher
    if (node.cipher) {
        vmessConfig.scy = node.cipher;
    }

    const jsonString = JSON.stringify(vmessConfig, null, 0);
    const base64String = Buffer.from(jsonString).toString('base64');
    return `vmess://${base64String}`;
}

function convertToVless(node) {
    const uuid = node.uuid;
    const server = node.server;
    const port = node.port;
    const params = new URLSearchParams();

    params.append('type', node.network);

    if (node.tls) {
        params.append('security', 'tls');
        if (node.servername) {
            params.append('sni', node.servername);
        }
    }

    if (node.network === 'ws' && node['ws-opts']) {
        if (node['ws-opts'].path) {
            params.append('path', node['ws-opts'].path);
        }
        if (node['ws-opts'].headers && node['ws-opts'].headers.Host) {
            params.append('host', node['ws-opts'].headers.Host);
        }
    }

    if (node.flow) {
        params.append('flow', node.flow);
    }
    params.append('encryption', node.encryption || 'none');

    const queryString = params.toString();
    const fragment = encodeURIComponent(node.name);

    return `vless://${uuid}@${server}:${port}?${queryString}#${fragment}`;
}

// --- Main Execution ---

function main() {
    const filePath = process.argv[2];

    if (!filePath) {
        console.error("Usage: node json-to-link.js <path/to/your/nodes.json>");
        console.error("\nPlease provide the path to the JSON file containing the node configurations.");
        // As an example, create a file named 'nodes.json' with the following content:
        const exampleJson = [
            {
              "name": "clawcloud-Germany",
              "type": "vmess",
              "server": "tfllremqaqvd.eu-central-1.clawcloudrun.com",
              "port": 443,
              "uuid": "78c9ec39-0bc8-4c69-82ad-77a1e24b1d63",
              "alterId": "0",
              "cipher": "auto",
              "tls": true,
              "skip-cert-verify": true,
              "network": "ws",
              "servername": "tfllremqaqvd.eu-central-1.clawcloudrun.com",
              "ws-opts": {
                "path": "/ws",
                "headers": {
                  "Host": "tfllremqaqvd.eu-central-1.clawcloudrun.com"
                }
              }
            }
        ];
        console.error("\nExample 'nodes.json' content:");
        console.error(JSON.stringify(exampleJson, null, 2));
        process.exit(1);
    }

    let nodes;
    try {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        nodes = JSON.parse(fileContent);
    } catch (error) {
        console.error(`Error reading or parsing file at ${filePath}:`, error.message);
        process.exit(1);
    }

    if (!Array.isArray(nodes)) {
        console.error("Input JSON must be an array of nodes.");
        process.exit(1);
    }

    const urls = nodes.map(node => {
        if (!node.type || !node.name) {
            console.warn(`Skipping node without 'type' or 'name'.`);
            return null;
        }
        switch (node.type.toLowerCase()) {
            case 'vmess':
                return convertToVmess(node);
            case 'vless':
                return convertToVless(node);
            default:
                console.warn(`Unsupported node type: '${node.type}' for node '${node.name}'`);
                return null;
        }
    }).filter(url => url !== null);

    if (urls.length > 0) {
        console.log(urls.join('\n'));
    } else {
        console.log("No compatible nodes found to convert.");
    }
}

main();
