const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建readline接口用于用户输入
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 提示用户输入的函数
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 递归获取目录下所有指定后缀的文件
function getCodeFiles(dir, extensions) {
    const files = [];
    
    function traverse(currentDir) {
        try {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // 跳过常见的非代码目录
                    if (!['node_modules', '.git', 'dist', 'build', '.next', 'coverage'].includes(item)) {
                        traverse(fullPath);
                    }
                } else if (stat.isFile()) {
                    const ext = path.extname(item).toLowerCase();
                    if (extensions.includes(ext)) {
                        files.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`警告: 无法读取目录 ${currentDir}: ${error.message}`);
        }
    }
    
    traverse(dir);
    return files;
}

// 清理代码内容，减少token使用
function cleanCodeContent(content, filePath, aggressiveMode = false) {
    const ext = path.extname(filePath).toLowerCase();

    // 移除BOM字符（UTF-8 BOM: \uFEFF）
    content = content.replace(/^\uFEFF/, '');

    // 移除行尾空格
    content = content.replace(/[ \t]+$/gm, '');

    // 移除单行注释（但保留重要的文档注释）
    if (['.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.cs'].includes(ext)) {
        // 保留以 /** 或 /// 开头的文档注释，移除普通的 // 注释
        content = content.replace(/^\s*\/\/(?!\/|\*).*$/gm, '');
    } else if (['.py'].includes(ext)) {
        // Python: 保留文档字符串，移除普通的 # 注释
        content = content.replace(/^\s*#(?!!).*$/gm, '');
    }

    // 移除多行注释中的非文档注释
    if (['.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.cs'].includes(ext)) {
        // 移除多行注释（但保留文档注释 /** */）
        content = content.replace(/\/\*(?!\*)([\s\S]*?)\*\//g, '');
    }

    // 强力清理空行：
    if (aggressiveMode) {
        // 激进模式：移除所有空行
        content = content.replace(/^\s*$/gm, '');
        content = content.replace(/\n+/g, '\n');
    } else {
        // 1. 先将所有只包含空白字符的行替换为完全空行
        content = content.replace(/^\s+$/gm, '');

        // 2. 将连续的多个空行合并为最多一个空行
        content = content.replace(/\n{3,}/g, '\n\n');

        // 3. 移除文件开头和结尾的空行
        content = content.replace(/^\n+/, '').replace(/\n+$/, '');

        // 4. 最后一次清理：确保没有超过两个连续的换行符
        content = content.replace(/\n{2,}/g, '\n\n');
    }

    return content;
}

// 显示帮助信息
function showHelp() {
    console.log('=== 代码文件整合工具 ===\n');
    console.log('用法:');
    console.log('  node code-aggregator.js [选项]\n');
    console.log('选项:');
    console.log('  -f <目录路径>    指定要扫描的目录路径，多个目录用逗号分隔');
    console.log('  -h, --help      显示此帮助信息\n');
    console.log('示例:');
    console.log('  node code-aggregator.js');
    console.log('  node code-aggregator.js -f "C:\\Project\\src"');
    console.log('  node code-aggregator.js -f "C:\\Project\\src,C:\\Project\\lib"\n');
}

// 解析命令行参数
function parseCommandLineArgs() {
    const args = process.argv.slice(2);
    const result = {};

    for (let i = 0; i < args.length; i++) {
        if (args[i] === '-f' && i + 1 < args.length) {
            result.folders = args[i + 1];
            i++; // 跳过下一个参数，因为它是 -f 的值
        } else if (args[i] === '-h' || args[i] === '--help') {
            result.showHelp = true;
        }
    }

    return result;
}

// 主函数
async function main() {
    try {
        // 解析命令行参数
        const cmdArgs = parseCommandLineArgs();

        // 如果请求帮助，显示帮助信息并退出
        if (cmdArgs.showHelp) {
            showHelp();
            return;
        }

        console.log('=== 代码文件整合工具 ===\n');

        // 获取用户输入
        let targetDirInput;
        if (cmdArgs.folders) {
            targetDirInput = cmdArgs.folders;
            console.log(`📂 使用命令行参数指定的目录: ${targetDirInput}`);
        } else {
            targetDirInput = await askQuestion('请输入要扫描的目录路径（多个目录用逗号分隔）: ');
        }
        const extensionsInput = await askQuestion('请输入文件后缀（用逗号分隔，默认: .js,.ts,.py,.cs,.java,.cpp,.c,.h,.cs): ') || '.js,.ts,.py,.cs,.java,.cpp,.c,.h,.cs';
        const outputFile = await askQuestion('请输入输出文件名（默认: aggregated-code.txt): ') || 'aggregated-code.txt';
        const aggressiveMode = await askQuestion('是否启用激进模式（移除所有空行）？(y/N): ');
        const useAggressiveMode = aggressiveMode.toLowerCase() === 'y' || aggressiveMode.toLowerCase() === 'yes';

        // 处理目录路径并去重
        const targetDirs = [...new Set(targetDirInput.split(',').map(dir => dir.trim()).filter(dir => dir.length > 0))];

        // 处理文件后缀
        const extensions = extensionsInput.split(',').map(ext => {
            ext = ext.trim();
            return ext.startsWith('.') ? ext : '.' + ext;
        });

        console.log(`\n=== 扫描配置 ===`);
        console.log(`目录数量: ${targetDirs.length}`);
        console.log(`文件后缀: ${extensions.join(', ')}`);
        console.log(`激进模式: ${useAggressiveMode ? '启用' : '禁用'}`);

        // 检查目录是否存在
        const invalidDirs = targetDirs.filter(dir => !fs.existsSync(dir));
        if (invalidDirs.length > 0) {
            console.error(`\n❌ 错误: 以下目录不存在:`);
            invalidDirs.forEach(dir => console.error(`   - ${dir}`));
            return;
        }

        // 获取所有代码文件（从多个目录）
        console.log(`\n=== 开始扫描目录 ===`);
        let codeFiles = [];
        let totalDirFiles = 0;

        for (let i = 0; i < targetDirs.length; i++) {
            const targetDir = targetDirs[i];
            console.log(`\n📁 [${i + 1}/${targetDirs.length}] 处理目录: ${targetDir}`);

            const dirFiles = getCodeFiles(targetDir, extensions);
            console.log(`   找到 ${dirFiles.length} 个文件`);

            codeFiles = codeFiles.concat(dirFiles);
            totalDirFiles += dirFiles.length;
        }

        // 去除重复的文件（使用Set来去重）
        const originalCount = codeFiles.length;
        codeFiles = [...new Set(codeFiles)];
        const duplicateCount = originalCount - codeFiles.length;

        console.log(`\n=== 文件统计 ===`);
        console.log(`📊 总计扫描: ${totalDirFiles} 个文件`);
        if (duplicateCount > 0) {
            console.log(`🔄 去除重复: ${duplicateCount} 个文件`);
        }
        console.log(`✅ 最终处理: ${codeFiles.length} 个文件`);

        if (codeFiles.length === 0) {
            console.log('\n❌ 未找到匹配的代码文件!');
            return;
        }

        console.log(`\n=== 开始整合代码 ===`);

        // 整合代码内容
        let aggregatedContent = '';
        let totalOriginalSize = 0;
        let totalCleanedSize = 0;

        // 添加文件列表
        aggregatedContent += '# 代码文件整合\n\n';
        aggregatedContent += `## 扫描目录\n${targetDirs.map(dir => `- ${dir}`).join('\n')}\n\n`;
        aggregatedContent += `## 文件列表 (共 ${codeFiles.length} 个文件)\n\n`;
        codeFiles.forEach((file, index) => {
            // 找到文件所属的目录，计算相对路径
            let relativePath = file;
            for (const targetDir of targetDirs) {
                if (file.startsWith(targetDir)) {
                    relativePath = path.relative(targetDir, file);
                    break;
                }
            }
            aggregatedContent += `${index + 1}. ${relativePath}\n`;
        });
        aggregatedContent += '\n---\n\n';

        // 处理每个文件
        for (let i = 0; i < codeFiles.length; i++) {
            const filePath = codeFiles[i];
            // 找到文件所属的目录，计算相对路径
            let relativePath = filePath;
            for (const targetDir of targetDirs) {
                if (filePath.startsWith(targetDir)) {
                    relativePath = path.relative(targetDir, filePath);
                    break;
                }
            }
            
            try {
                // 显示进度，每10个文件显示一次详细信息
                if (i % 10 === 0 || i === codeFiles.length - 1) {
                    console.log(`📄 [${i + 1}/${codeFiles.length}] ${relativePath}`);
                }

                const content = fs.readFileSync(filePath, 'utf8');
                const cleanedContent = cleanCodeContent(content, filePath, useAggressiveMode);

                totalOriginalSize += content.length;
                totalCleanedSize += cleanedContent.length;

                // 添加文件分隔符和内容
                aggregatedContent += `## 文件: ${relativePath}\n\n`;
                aggregatedContent += '```' + path.extname(filePath).substring(1) + '\n';
                aggregatedContent += cleanedContent;
                aggregatedContent += '\n```\n\n';
                aggregatedContent += '---\n\n';

            } catch (error) {
                console.warn(`⚠️  警告: 无法读取文件 ${relativePath}: ${error.message}`);
            }
        }
        
        // 写入输出文件
        const outputPath = path.resolve(outputFile);
        fs.writeFileSync(outputPath, aggregatedContent, 'utf8');
        
        // 显示统计信息
        console.log('\n🎉 === 整合完成 ===');
        console.log(`📁 输出文件: ${outputPath}`);
        console.log(`📊 处理统计:`);
        console.log(`   - 扫描目录: ${targetDirs.length} 个`);
        console.log(`   - 处理文件: ${codeFiles.length} 个`);
        console.log(`   - 原始大小: ${(totalOriginalSize / 1024).toFixed(2)} KB`);
        console.log(`   - 优化后大小: ${(totalCleanedSize / 1024).toFixed(2)} KB`);
        console.log(`   - 压缩率: ${((1 - totalCleanedSize / totalOriginalSize) * 100).toFixed(1)}%`);
        console.log(`   - 最终文件大小: ${(aggregatedContent.length / 1024).toFixed(2)} KB`);
        console.log(`\n✨ 代码整合完成，可用于大模型输入！`);
        
    } catch (error) {
        console.error('发生错误:', error.message);
    } finally {
        rl.close();
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { getCodeFiles, cleanCodeContent };
