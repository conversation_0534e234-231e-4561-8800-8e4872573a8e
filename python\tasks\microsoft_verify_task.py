import json
import logging
import asyncio
import signal
import sys
import random
import time
from playwright.async_api import <PERSON><PERSON>,<PERSON>,Frame
from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from utils.cache_helper import handle_route
from utils.captcha_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.config_manager import ConfigManager
from utils.mail_verification_helper import MailVerificationHelper
from utils.sms_helper import SmsHelper
from urllib.parse import urlparse, parse_qs


class MicrosoftAccountVerifyTask:
    """
    邮箱验证任务 验证手机号和绑定邮箱
    """
    def __init__(
        self,
        config_manager: ConfigManager,
        account_unlock: bool = True,
    ) -> None:
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.sms_token = None
        self.browserUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
        self.captcha_helper = CaptchaHelper(
            two_captcha_key=self.config_manager.two_captcha_key,
            captcharun_token=self.config_manager.captcharun_token,
            browser_user_agent=self.browserUserAgent
        )
        self.account_unlock = account_unlock

    async def handler_single_account(self, page: Page, email: str, password: str, index: int = 0, total: int = 0, max_retries: int = 4):
        """执行登录操作"""
        retry_count = 0
        while retry_count < max_retries:
            try:
                self.logger.info(f"正在执行第 {index}/{total} 个账号 - 邮箱: {email} 密码: {password} (第 {retry_count + 1} 次尝试)")
                
                # 清除所有cookies
                await page.context.clear_cookies()
                
                # 随机选择一个 proof_email 配置并初始化 verification_helper
                #random.choice(self.config_manager.proof_email)
                proof_email_config = self.config_manager.proof_email[0] 
                proof_domain = proof_email_config['domain']
                proof_api = proof_email_config['api']
                proof_email = f"{email.split('@')[0]}@{proof_domain}"
                # 输入邮箱
                await page.goto("https://login.microsoftonline.com/", timeout=60000,wait_until='domcontentloaded')
                await page.fill('input[type="email"]', email)
                await asyncio.sleep(3)
                await page.click('input[type="submit"]')
                
                try:
                    await page.wait_for_selector('#i0116Error', timeout=5000)
                    retry_count = max_retries
                    raise Exception("帐户不存在")
                except PlaywrightTimeoutError as e:
                    self.logger.info(f"帐户存在,继续: {str(e)}")
                
                try:
                    await page.wait_for_selector('#usernameError', timeout=5000)
                    retry_count = max_retries
                    raise Exception("帐户不存在")
                except PlaywrightTimeoutError as e:
                    self.logger.info(f"帐户存在,继续: {str(e)}")
                
                
                # 输入密码
                await page.wait_for_url('https://login.live.com/**', timeout=60000)
                
      
                try:
                    await page.wait_for_selector('#idA_PWD_SwitchToPassword', timeout=3000)
                    await page.click('#idA_PWD_SwitchToPassword')
                except Exception as e:
                    self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
                
                
                await page.fill('input[type="password"]', password)
                await asyncio.sleep(3)
                await page.click('button[type="submit"]')
                
                
                # 等待手机验证
                service_abuseLanding = False
                try:
                    await page.wait_for_url(lambda url: url.startswith('https://account.live.com/Abuse'), timeout=60000)
                    service_abuseLanding = True
                except Exception as e:
                    self.logger.info(f"没有手机验证页面，继续执行: {str(e)}")
       
       
                if service_abuseLanding:
                    if(self.account_unlock == False):
                        retry_count = max_retries
                        raise Exception("账号被锁定")
                    #开始解锁
                    await page.click('button#StartAction')
                    #获取手机号
                    result = await SmsHelper.retry_request(
                        f"https://api.haozhuma.cn/sms/?api=getPhone&token={self.sms_token}&sid=21641"
                    )
                    phone = result['phone']
                    try:
                        await page.select_option('#phoneCountry', "CN")
                        await page.fill('#proofField', phone)
                        await asyncio.sleep(3)
                        await page.click('button[type="submit"]')
                    
                        try:
                            await page.wait_for_selector('#SmsBlockTitle', timeout=5000)
                            retry_count = max_retries
                            raise Exception("手机验证被阻止")
                        except PlaywrightTimeoutError as e:
                            self.logger.info(f"手机验证没有被阻止,继续: {str(e)}")

                        try:
                            # 获得sitekey
                            await page.wait_for_selector('#enforcementFrame', timeout=5000)
                            enforcement_frame = page.frame_locator('#enforcementFrame')
                            verification_challenge = enforcement_frame.frame_locator('iframe')
                            fc_token_input = verification_challenge.locator('#FunCaptcha-Token').first
                            fc_token = await fc_token_input.input_value()
                            self.logger.info(f"fc_token:{fc_token}")
                            params = dict(param.split('=', 1) for param in fc_token.split('|') if '=' in param)
                            sitekey =  params.get('pk', '')
                            surl = params.get('surl', '').replace('%2F', '/').replace('%3A', ':')
                            self.logger.info(f"url:{page.url},sitekey:{sitekey}.surl:{surl}")
                            # 获得url中data值
                            enforcementFrameSrc = await (page.locator('#enforcementFrame').get_attribute('src'))
                            parsed_url = urlparse(enforcementFrameSrc)
                            query_params = parse_qs(parsed_url.query)
                            data_value = query_params.get('data', [''])[0]
                            code = self.captcha_helper.get_captcharun_token(site_key=sitekey, blob=data_value,surl=surl,siteReferer="https://signup.live.com/signup")
                            self.logger.info(f"token:{str(code)}")
                            # 构造JavaScript代码字符串,注入脚本
                            script = '''
                            (token) => {
                                console.log('token:',token)
                                // 这里的代码在 iframe 的上下文中运行
                                let script = document.createElement('SCRIPT');
                                script.append('function captchaSubmit(token) { parent.postMessage(JSON.stringify({ eventId: "challenge-complete", payload: { sessionToken: token } }), "*") }');
                                document.documentElement.appendChild(script);
                                captchaSubmit(token); // 直接调用函数
                            }
                            '''
                            frame: Frame = page.frame(url=enforcementFrameSrc) 
                            await frame.evaluate(script, code)
                        except Exception as e:
                            self.logger.info(f"没有机器人验证,继续: {str(e)}")
                    
                        # 获取验证码
                        result = await SmsHelper.retry_request(
                            f"https://api.haozhuma.cn/sms/?api=getMessage&token={self.sms_token}&sid=21641&phone={phone}",max_retries=20
                        )
                        yzm = result['yzm']
                        self.logger.info(f"获取到验证码: {yzm}")
                        # Wait for the input element to be visible and enabled
                        await page.wait_for_selector('#enter-code-input', state='visible')
                        # Clear the input field first
                        await page.fill('#enter-code-input', '')
                        # Fill in the verification code
                        await page.fill('#enter-code-input', yzm)
                        # Wait for the button to be clickable
                        await page.wait_for_selector('button[type="submit"]', state='visible')
                        # Click the submit button
                        await page.click('button[type="submit"]')
                        
                        # 完成验证码验证
                        await page.wait_for_selector('button#FinishAction', state='visible')
                        await page.click('button#FinishAction')
                    finally:
                        #释放手机号
                        await SmsHelper.retry_request(
                            f"https://api.haozhuma.cn/sms/?api=cancelRecv&token={self.sms_token}&sid=21641&phone={phone}"
                        )
                        
                # 处理多重验证
                try:
                    await page.wait_for_url('https://account.live.com/proofs/**', timeout=3000)
                    
                    hasOption = False
                    try:
                        await page.wait_for_selector("#iProof0", timeout=1000)
                        hasOption = True
                    except Exception as e:
                        self.logger.info(f"没有选择email: {str(e)}")
                        
                    
                    timestamp = int(time.time())
                    if hasOption:
                        await page.click('#iProof0')
                        await page.click('input[type="submit"]')
                    else:
                        await page.fill("#EmailAddress", proof_email) 
                        await page.click('input[type="submit"]')

                    # 获取验证码
                    verification_code = await MailVerificationHelper.get_ms_verification_code(
                        proof_api,
                        proof_email,
                        timestamp
                    )
                    await page.fill('input[type="tel"]', verification_code)
                    await page.click('input[type="submit"]')
                except Exception as e:
                    self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")
                
                # 处理隐私通知
                try:
                    await page.wait_for_url('https://privacynotice.account.microsoft.com/**', timeout=3000)
                    await page.click('#id__0')
                except Exception as e:
                    self.logger.info(f"没有隐私通知页面，继续执行: {str(e)}")
                
                # 点击登录
                try:
                    await page.wait_for_url('https://login.live.com/**', timeout=3000)
                    await page.click('button[type="submit"]')
                except Exception as e:
                    self.logger.info(f"没有登陆页面，继续执行: {str(e)}")
                    
                self.logger.info(f'手机验证和绑定邮箱成功 for {email}')
                break
            
            except Exception as e:
                retry_count += 1
                self.logger.error(f"Login attempt {retry_count} failed for {email}: {str(e)}")
                
                if retry_count >= max_retries:
                    self.logger.error(f"Max retries reached for {email}, moving to next account")
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                
                # 等待一段时间后重试
                await asyncio.sleep(random.uniform(5, 10))
                continue


    async def do(self, playwright: Playwright, accounts: list, max_concurrent_tasks: int = 1):
        failed_accounts = []
        success_accounts = []  # 新增成功账户列表
        
        # 获取短信平台token
        result = await SmsHelper.retry_request(
            f"https://api.haozhuma.cn/sms/?api=login&user={self.config_manager.sms_api_account}&pass={self.config_manager.sms_api_password}"
        )
        self.sms_token = result['token']
        self.logger.info(f"获取到短信平台token: {self.sms_token}")
        await SmsHelper.retry_request(
            f"https://api.haozhuma.cn/sms/?api=cancelAllRecv&token={self.sms_token}"
        )
        
        sem = asyncio.Semaphore(max_concurrent_tasks)
        tasks = []
        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            # Windows使用默认信号处理
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)
        
        # 定义具有信号量的处理任务函数
        async def process_account_group(account_group):
            async with sem:
                return await self.batch(playwright, account_group)
        

        # 假设max_concurrent_tasks代表组数
        accounts_per_group = len(accounts)//max_concurrent_tasks
        remainder = len(accounts) % max_concurrent_tasks
 
        account_groups = []
        start_idx = 0

        for i in range(max_concurrent_tasks):
            # 如果有余数，前remainder组多分配一个账号
            group_size = accounts_per_group + (1 if i < remainder else 0)
            account_groups.append(accounts[start_idx:start_idx + group_size])
            start_idx += group_size

        # 创建并跟踪所有任务
        for account_group in account_groups:
            task = asyncio.create_task(process_account_group(account_group))
            tasks.append(task)
    
        try:
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 聚合异常结果
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed: {str(result)}")
                elif isinstance(result, tuple):  # 修改为返回元组 (success_list, failed_list)
                    success, failed = result
                    success_accounts.extend(success)
                    failed_accounts.extend(failed)
                    
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)
    
        return success_accounts, failed_accounts  # 返回成功和失败的账户列表
    
    async def batch(self, playwright: Playwright, accounts: list):
        failed_accounts = []
        success_accounts = []  # 新增成功账户列表
        total = len(accounts)
        
        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': ['--disable-web-security']
        }
        context_options = {
            'ignore_https_errors': True,
            'user_agent': self.browserUserAgent,
        }
        # Get available proxies from config
        proxies = self.config_manager.get('proxies', [])
        if proxies:
            proxy = random.choice(proxies)
            if proxy.get('host') and proxy['host'].strip() != "":
                proxy_config = {
                    'server': proxy['host'],
                }
                
                # Add authentication if provided
                if proxy.get('username') and proxy.get('password'):
                    proxy_config['username'] = proxy['username']
                    proxy_config['password'] = proxy['password']
                
                context_options['proxy'] = proxy_config
                self.logger.info(f"Using proxy: {proxy['host']}")
            
        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)
        await context.route('**/*', handle_route)
        page = await context.new_page()
        try:            
            for index, account in enumerate(accounts, 1):
                try:
                    account_parts = account.strip().split(':')
                    email = account_parts[0]
                    password = account_parts[1]
                    await self.handler_single_account(page, email, password, index, total)
                    success_accounts.append(account)  # 添加成功的账户
                    # 成功后等待一段随机时间，避免操作过于频繁
                    await asyncio.sleep(random.uniform(3, 5))
                except Exception as e:
                    logging.error(f"第 {index}/{total} 个账号处理失败 {email}: {str(e)}")
                    failed_accounts.append(f"{email}:{password}:{str(e)}")
        finally:
            await page.close()
            await context.close()
            await browser.close()
            
        return success_accounts, failed_accounts  # 返回成功和失败的账户列表