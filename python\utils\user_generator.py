import random
from datetime import datetime, timedelta
from uuid import uuid4
from typing import Dict
from unique_names_generator import get_random_name
from unique_names_generator.data import ADJECTIVES, ANIMALS, COLORS, COUNTRIES, LANGUAGES, NAMES, STAR_WARS
from .config_manager import ConfigManager

class User:
    def __init__(self, email_prefix: str, email_domain: str, country_region: str, password: str, first_name: str, last_name: str, birthday: str):
        self.email_prefix = email_prefix
        self.email_domain = email_domain
        self.country_region = country_region
        self.password = password
        self.first_name = first_name
        self.last_name = last_name
        self.birthday = birthday

class UserGenerator:
    def __init__(self, config: ConfigManager):
        self.config = config

    @staticmethod
    def _generate_random_string(length: int = 8) -> str:
        """生成指定长度的随机字符串"""
        while True:
            rnd = str(uuid4()).upper().replace("-", "")
            if not rnd[0:length][:1].isdigit():
                return rnd[0:length]

    def _generate_random_date(self) -> str:
        """生成随机生日，格式：YYYY-MM-DD"""
        start_year = self.config.get('user_info.min_birth_date', 1980)
        end_year = self.config.get('user_info.max_birth_date', 1999)
        start_date = datetime(start_year, 1, 1)
        end_date = datetime(end_year, 12, 31)
        time_between_dates = end_date - start_date
        days_between_dates = time_between_dates.days
        random_number_of_days = random.randrange(days_between_dates)
        random_date = start_date + timedelta(days=random_number_of_days)
        return random_date.strftime("%Y-%m-%d")

    def _generate_email_prefix(self) -> str:
        """生成邮箱前缀"""
        while True:
            try:
                name = get_random_name(
                    combo=[NAMES, random.choice([ADJECTIVES, ANIMALS, COLORS,  LANGUAGES, NAMES])],
                    separator=""
                ).replace(" ", "")

                # 随机替换一个字符
                pos = random.randint(0, len(name) - 1)
                name = name[:pos] + self._generate_random_string(1) + name[pos + 1:]

                # 随机末尾添加字符
                name += self._generate_random_string(1)

                # 随机末尾添加数字
                if random.randint(0, 1):
                    name += str(random.randint(0, 999))

                return name.lower()
            except:
                continue

    def _generate_password(self) -> str:
        """生成随机密码"""
        length = self.config.get('user_info.password_length', 12)
        chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))

    def _generate_name(self) -> tuple:
        """生成随机姓名"""
        first_name = get_random_name(combo=[NAMES], separator="")
        last_name = get_random_name(combo=[NAMES], separator="")
        return first_name, last_name

    def generate_user(self) -> User:
        """生成用户信息"""
        first_name, last_name = self._generate_name()
        birthday = self._generate_random_date()
        return User(
            email_prefix=self._generate_email_prefix(),
            email_domain=self.config.get('user_info.domain', '@outlook.com'),
            country_region=self.config.get('user_info.country_region', 'CN'),
            password=self._generate_password(),
            first_name=first_name,
            last_name=last_name,
            birthday=birthday
        )

def main():
    """主函数，展示UserGenerator的使用方法"""
    # 创建生成器实例
    generator = UserGenerator()
    
    print("** 生成单个用户信息 **")
    user = generator.generate_user()
    print(f"邮箱前缀: {user.email_prefix}")
    print(f"密码: {user.password}")
    print(f"姓名: {user.first_name} {user.last_name}")
    print(f"生日: {user.birthday}")
    print("\n")

    print("** 批量生成用户信息 **")
    # 生成5个用户并保存到文件
    users = []
    for i in range(5):
        user = generator.generate_user()
        users.append(user)
        # 保存到文件
        with open('test_users.txt', 'a', encoding='utf-8') as f:
            f.write(f"用户 {i+1}:\n")
            f.write(f"邮箱前缀: {user.email_prefix}\n")
            f.write(f"密码: {user.password}\n")
            f.write(f"姓名: {user.first_name} {user.last_name}\n")
            f.write(f"生日: {user.birthday}\n")
            f.write("-" * 50 + "\n")
    
    print(f"已生成 {len(users)} 个用户信息并保存到 test_users.txt")
    
    # 展示生成的用户列表
    print("\n** 生成的用户列表 **")
    for i, user in enumerate(users, 1):
        print(f"\n用户 {i}:")
        print(f"邮箱: {user.email_prefix}@example.com")
        print(f"姓名: {user.first_name} {user.last_name}")


if __name__ == '__main__':
    main()