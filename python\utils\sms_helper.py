import logging
import asyncio
import random
import requests
from typing import Optional, Dict, Any

class SmsHelper:
    @staticmethod
    async def retry_request(url: str, max_retries: int = 3, delay: tuple = (5, 10)) -> Dict[Any, Any]:
        """
        统一的重试函数，用于处理短信平台 API 请求
        
        Args:
            url: API 请求地址
            max_retries: 最大重试次数
            delay: 重试延迟时间范围，(最小值, 最大值)
            
        Returns:
            Dict: API 响应结果
            
        Raises:
            Exception: 当达到最大重试次数后仍然失败时抛出异常
        """
        logger = logging.getLogger(__name__)
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                response = requests.get(url, headers={}, data={})
                response.raise_for_status()
                result = response.json()
              
                if result['code'] == 0 or result['code'] == "0" or result['code'] == "200" or result['code'] == 200:
                    return result
                    
                raise Exception(f"API returned error code: {result['code']}, message: {result.get('msg', 'Unknown error')}")
                
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                
                wait_time = random.uniform(delay[0], delay[1])
                logger.warning(f"Attempt {retry_count} failed, retrying in {wait_time:.2f} seconds: {str(e)}")
                await asyncio.sleep(wait_time)
        
        raise Exception("Unexpected error in retry_request")
