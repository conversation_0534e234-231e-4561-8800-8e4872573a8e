import aiohttp
import asyncio
import json
from pathlib import Path
import logging
import sys
from typing import List, Dict, Optional, Union
import time


class ProxyTestTask:
    def __init__(self, config_manager=None, default_protocol="socks5"):
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 更改测试URL为更可靠的选项
        self.test_urls = [
            "https://ifconfig.me/ip",   # 主要测试URL
            "https://httpbin.org/ip",  # 备用URL
            "https://api.ipapi.is",
            "https://myip.ipip.net/", 
        ]
        self.config_manager = config_manager
        self.default_protocol = default_protocol
        self.progress = 0
        self.total = 0

    def _parse_proxy(self, proxy: str) -> Optional[Dict]:
        """解析代理字符串格式"""
        try:
            protocol = self.default_protocol
            username = ""
            password = ""
            
            if '://' in proxy:
                protocol, remainder = proxy.split('://', 1)
                protocol = protocol.lower()
            else:
                remainder = proxy
            
            if '@' in remainder:
                auth_part, host_port = remainder.split('@', 1)
                if ':' in auth_part:
                    username, password = auth_part.split(':', 1)
            else:
                host_port = remainder
            
            host_parts = host_port.split(':')
            if len(host_parts) != 2:
                self.logger.warning(f"Invalid proxy format: {proxy}")
                return None
            
            host, port = host_parts
            
            return {
                "protocol": protocol,
                "host": host,
                "port": port,
                "username": username,
                "password": password
            }
        except Exception as e:
            self.logger.error(f"Parsing proxy {proxy} failed: {str(e)}")
            return None

    async def test_proxy(self, proxy: str) -> Optional[Dict]:
        try:
            self.logger.info(f"Testing proxy: {proxy}")
            
            parsed = self._parse_proxy(proxy)
            if not parsed:
                self.progress += 1
                self._update_progress()
                return None
                
            # 构建完整的代理URL
            if parsed['username'] and parsed['password']:
                proxy_url = f"{parsed['protocol']}://{parsed['username']}:{parsed['password']}@{parsed['host']}:{parsed['port']}"
            else:
                proxy_url = f"{parsed['protocol']}://{parsed['host']}:{parsed['port']}"
            
            # 尝试多个测试URL以提高成功率
            for test_url in self.test_urls:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            test_url,
                            proxy=proxy_url,
                            timeout=aiohttp.ClientTimeout(total=10)
                        ) as response:
                            if response.status == 200:
                                # 验证响应是否包含IP信息
                                content = await response.text()
                                if "ip" in content.lower() or parsed['host'] in content:
                                    result = {
                                        "host": f"{parsed['protocol']}://{parsed['host']}:{parsed['port']}",
                                        "username": parsed['username'],
                                        "password": parsed['password']
                                    }
                                    self.logger.info(f"[SUCCESS] Proxy {proxy} working: {content.replace('\n','')}")
                                    self.progress += 1
                                    self._update_progress()
                                    return result
                except Exception as e:
                    self.logger.debug(f"Failed with URL {test_url} for proxy {proxy}: {str(e)}")
            
            self.logger.debug(f"Proxy {proxy} failed all test URLs")
            self.logger.info(f"[FAILED] Proxy not working: {proxy}")
            self.progress += 1
            self._update_progress()
            return None
        except Exception as e:
            self.logger.error(f"Testing proxy {proxy} failed: {str(e)}")
            self.progress += 1
            self._update_progress()
            return None

    def format_proxy(self, proxy: str) -> Optional[Dict]:
        """仅转换代理格式而不进行测试"""
        parsed = self._parse_proxy(proxy)
        if not parsed:
            return None
            
        return {
            "host": f"{parsed['protocol']}://{parsed['host']}:{parsed['port']}",
            "username": parsed['username'],
            "password": parsed['password']
        }

    def _update_progress(self):
        """更新并显示进度"""
        percentage = (self.progress / self.total) * 100 if self.total > 0 else 0
        bar_length = 30
        filled_length = int(bar_length * self.progress // self.total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        sys.stdout.write(f'\r进度: |{bar}| {self.progress}/{self.total} ({percentage:.2f}%) ')
        sys.stdout.flush()
        
        # 如果完成所有测试，打印一个新行
        if self.progress >= self.total:
            print()

    async def test_proxies(self, proxy_list: List[str], max_concurrent: int = 10) -> List[Dict]:
        self.total = len(proxy_list)
        self.progress = 0
        
        # 询问用户是否只需要转换格式
        format_only = input("是否只转换格式而不测试代理? (y/n): ").lower().strip() == 'y'
        
        if format_only:
            self.logger.info("仅转换代理格式...")
            results = []
            for proxy in proxy_list:
                result = self.format_proxy(proxy)
                if result:
                    results.append(result)
            self.logger.info(f"格式转换完成: {len(results)} 个代理")
            return results
            
        # 以下是测试代理的逻辑
        working_proxies = []
        semaphore = asyncio.Semaphore(max_concurrent)
        start_time = time.time()

        print(f"开始测试 {self.total} 个代理，最大并发数: {max_concurrent}")
        self._update_progress()

        async def test_proxy_with_semaphore(proxy: str):
            async with semaphore:
                return await self.test_proxy(proxy)

        tasks = [test_proxy_with_semaphore(proxy) for proxy in proxy_list]
        
        self.logger.info(f"Testing {len(tasks)} proxies with max concurrency of {max_concurrent}")
        results = await asyncio.gather(*tasks)
        working_proxies = [proxy for proxy in results if proxy is not None]

        elapsed_time = time.time() - start_time
        print(f"\n测试完成！总共测试 {self.total} 个代理，找到 {len(working_proxies)} 个可用代理")
        print(f"耗时: {elapsed_time:.2f} 秒")

        return working_proxies

# 使用示例
async def main():
    # 为控制台输出设置适当的编码处理
    console_handler = logging.StreamHandler(sys.stdout)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[console_handler]
    )
    
    logger = logging.getLogger(__name__)
    
    # 设置默认协议为 https (可以是 "https" 或 "socks5")
    tester = ProxyTestTask(default_protocol="https")
    
    # 直接测试列表中的代理，包括带有用户名密码的代理
    # proxies_to_test = [
    #     "8.213.222.247:8443",
    #     "101.47.152.217:20000",
    #     "72.10.160.90:11595",
    #     "172.67.70.71:80",
    #     "https://172.67.70.71:80",  # 显式指定https协议
    #     "socks5://72.10.160.90:11595",  # 显式指定socks5协议
    #     "username:password@***********:8080",  # 带认证信息的代理
    #     "******************************************"  # 带协议和认证信息的代理
    # ]
    proxies_to_test = [
        "****************************************"
    ]
    
    # 设置最大并发数为20
    results = await tester.test_proxies(proxies_to_test, max_concurrent=20)
    
    # 输出结果
    for proxy in results:
        if proxy['username'] and proxy['password']:
            logger.info(f"  - {proxy['host']} (auth: {proxy['username']}:***)")
        else:
            logger.info(f"  - {proxy['host']}")

if __name__ == "__main__":
    asyncio.run(main())
