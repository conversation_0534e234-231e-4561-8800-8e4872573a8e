import logging
import asyncio
import random
import signal
import sys
from playwright.async_api import <PERSON><PERSON>,Page
from utils.config_manager import ConfigManager
import time
from utils.mail_verification_helper import MailVerificationHelper

class MicrosoftRewardPCTask:
    """
    Microsoft Rewards 奖励任务
    """
    def __init__(
        self,
        config_manager: ConfigManager
    ) -> None:
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager

    async def handler_single_account(self, page: Page, email: str, password: str, index: int = 0, max_retries: int = 3):
        """执行奖励任务"""
        retry_count = 0
        while retry_count < max_retries:
            try:
                self.logger.info(f"开始执行奖励任务 [{index}]: {email} (尝试 {retry_count + 1})")
                
                # 清除所有cookies
                await page.context.clear_cookies()

                # 访问奖励页面
                await page.goto("https://rewards.bing.com")
                # 输入邮箱
                await page.wait_for_url('https://login.live.com/oauth20_authorize.srf**', timeout=3000)
                await page.fill('input[type="email"]', email)
                await page.click('button[type="submit"]')
      
                # 输入密码
                await page.fill('input[type="password"]', password)
                await page.click('button[type="submit"]')


                # 处理多重验证
                for i in range(2):
                    try:
                        await page.wait_for_url('https://account.live.com/identity/**', timeout=3000)
                        proof_email_config = self.config_manager.proof_email[0]
                        proof_domain = proof_email_config['domain']
                        proof_api = proof_email_config['api']
                        proof_email = f"{email.split('@')[0]}@{proof_domain}"
                        
                        try:
                            await page.wait_for_selector("#iProof0", timeout=1000)
                            await page.click('#iProof0')
                        except Exception as e:
                            self.logger.info(f"没有选择email: {str(e)}")
                        
                        await page.fill("#iProofEmail", proof_email)
                        timestamp = int(time.time())
                        await page.click('input[type="submit"]')
                        # 获取验证码
                        verification_code = await MailVerificationHelper.get_ms_verification_code(proof_api,proof_email, timestamp)
                        await page.fill('input[type="tel"]', verification_code)
                        await page.click('input[type="submit"]')
                    except Exception as e:
                        self.logger.info(f"没有处理多重验证，继续执行: {str(e)}")


                # 确认登录
                await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=3000)
                await page.click('button[type="submit"]')

                # 等待页面加载完成
                # try:
                #     await page.wait_for_selector('#welcome-tour', timeout=10000)
                #     await page.click('#welcome-tour #fre-next-button:visible')
                #     await asyncio.sleep(random.uniform(3, 5))
                #     await page.click('#welcome-tour #fre-next-button:visible')
                #     await asyncio.sleep(random.uniform(3, 5))
                #     await page.click('#welcome-tour #claim-button:visible')
                #     await asyncio.sleep(random.uniform(3, 5))
                #     await page.keyboard.press('Escape') # 这一步有问题.会弹窗让你选择edge固定开始栏,建议用手机登录
                #     await asyncio.sleep(random.uniform(3, 5))
                #     await page.click('#modal-host button.c-glyph.glyph-cancel:visible')
                # except Exception as e:
                #     self.logger.info(f"没有引导页面，继续执行: {str(e)}")

                await page.wait_for_selector("#daily-sets")
                modeOn = page.locator("#daily-sets #balanceToolTipDiv #ModeOn.toggleOff")
                if await modeOn.is_visible():
                    await page.click('#daily-sets #balanceToolTipDiv #ModeOn')

                await asyncio.sleep(random.uniform(3, 5))
           
                self.logger.info(f'完成初始化 {email}')
                break
                
            except Exception as e:
                retry_count += 1
                self.logger.error(f"奖励任务尝试 {retry_count} 失败 {email}: {str(e)}")
                
                if retry_count >= max_retries:
                    self.logger.error(f"达到最大重试次数 {email}")
                    raise Exception(f"Failed after {max_retries} attempts: {str(e)}")
                
                await asyncio.sleep(random.uniform(5, 10))
                continue

    
    async def do(self, playwright: Playwright, accounts: list, max_concurrent_tasks: int = 1):
        failed_accounts = []
        
        sem = asyncio.Semaphore(max_concurrent_tasks)
        
        tasks = []
        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            # Windows使用默认信号处理
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)
        
        # 定义具有信号量的处理任务函数
        async def process_account_group(account_group):
            async with sem:
                return await self.batch(playwright, account_group)
        
        # 假设max_concurrent_tasks代表组数
        accounts_per_group = len(accounts) // max_concurrent_tasks
        remainder = len(accounts) % max_concurrent_tasks

        account_groups = []
        start_idx = 0

        for i in range(max_concurrent_tasks):
            # 如果有余数，前remainder组多分配一个账号
            group_size = accounts_per_group + (1 if i < remainder else 0)
            account_groups.append(accounts[start_idx:start_idx + group_size])
            start_idx += group_size
        
        # 创建并跟踪所有任务
        for account_group in account_groups:
            task = asyncio.create_task(process_account_group(account_group))
            tasks.append(task)
    
        try:
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 聚合异常结果
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed: {str(result)}")
                elif isinstance(result, list):  # 正常返回的失败列表
                    failed_accounts.extend(result)
                    
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)
    
        return failed_accounts
            
        
    async def batch(self, playwright: Playwright, accounts: list):
        """批量处理奖励任务"""
        failed_accounts = []

        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': ['--disable-web-security']
        }
        
        context_options = {
            'ignore_https_errors': True
        }
        
        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)
        page = await context.new_page()
        
        try:
            for index, account in enumerate(accounts, 1):
                try:
                    email = account['email']
                    password = account['password']
                    await self.handler_single_account(page, email, password, index)
                    await asyncio.sleep(random.uniform(3, 5))
                except Exception as e:
                    self.logger.error(f"Account processing failed for [{index}] {email}: {str(e)}")
                    failed_accounts.append(account)
        finally:
            await page.close()
            await context.close()
            await browser.close()
            
        return failed_accounts