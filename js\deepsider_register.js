import { promises as fs } from 'fs';
import path from 'path';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
import { accounts, mailHeaders, getEmailContent } from './utils/msmail.js';

const password = "Dean0104@deepsider";
const passwordHash = "949594ce2916cb4ccca1dc772b30ee5b";
const invitationId = "67e20b242b68ea9fc2b1e64c"; //67e1157ea9219b3954559514
class DeepSiderRegister {

    constructor() {
        this.headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "i-lang": "zh-CN",
            "i-version": "1.1.68",
            "pragma": "no-cache",
            "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "cross-site"
        };

        this.directAgent = new Agent();
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async sendVerificationCode(email) {
        const response = await fetch("https://api.chargpt.ai/api/verify/send-email", {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify({
                email: email,
                type: "register"
            }),
            dispatcher: this.proxyAgent
        });

        //{"code":0,"message":"success","timestamp":1742806214344}
        //{"code":6001,"message":"Request rate limited","timestamp":1742818775502}
        return await response.json();
    }

    async register(email, code) {
        const response = await fetch("https://api.chargpt.ai/api/user/register", {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify({
                email: email,
                emailCode: code,
                password: passwordHash, // 默认密码
                invitationId: invitationId,
                gtag: {
                    utm_source: "unknow",
                    utm_medium: "unknow",
                    utm_campaign: "unknow",
                    utm_term: "unknow",
                    utm_content: "unknow",
                    utm_id: "unknow"
                },
                referer: ""
            })
        });
        //{"code":1005,"message":"邮箱验证码错误","timestamp":*************}
        //{"code":0,"message":"success","data":true,"timestamp":*************}
        return await response.json();
    }

    async saveAccount(email, result) {
        const accountInfo = `${email},${password},${result.token},${result.refreshToken}\n`;
        await fs.appendFile('data/deepsider_accounts.csv', accountInfo);
    }

    async login(email) {
        const response = await fetch("https://api.chargpt.ai/api/user/login", {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify({
                email: email,
                password: passwordHash
            })
        });
        //{"code":1003,"message":"用户名或密码错误","timestamp":*************}

        // {
        //     "autoLog": false,
        //     "timestamp": *************,
        //     "code": 0,
        //     "message": "success",
        //     "data": {
        //         "email": "<EMAIL>",
        //         "gtag": {
        //             "utm_source": "unknow",
        //             "utm_medium": "unknow",
        //             "utm_campaign": "unknow",
        //             "utm_term": "unknow",
        //             "utm_content": "unknow",
        //             "utm_id": "unknow"
        //         },
        //         "nickname": "bristgogall4",
        //         "portrait": "https://img.chatgot.io/public/avatar/avatar5.jpg",
        //         "platform": "common_email",
        //         "channelTag": "",
        //         "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.gYJjP-bAy8qgQ5JI3P2esHxcPeH5T3Vey2cROWeTDDE",
        //         "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.BIxOQZNETGV2f4XYUEKS60yVvUvTVopLXLw-RIksHzc"
        //     }
        // }
        return await response.json();
    }

    async refreshToken(refreshToken) {
        const response = await fetch("https://api.chargpt.ai/api/user/refreshtoken", {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify({
                refreshtoken: refreshToken
            })
        });

        // {
        //     "autoLog": false,
        //     "timestamp": 1742819273141,
        //     "code": 0,
        //     "message": "success",
        //     "data": {
        //         "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.YXpxvsGjlZYAXHxuJ9EcjKfkBtx0kCXsJx287BxQAw4",
        //         "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.eDi6SsLDpTj2o-X2xzBwufPYZ65n6pvZW0UTJ4eKnMY"
        //     }
        // }
        return await response.json();
    }

    async getProxyIP(maxRetries = 30) {
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                const proxyResponse = await fetch(
                    'http://api.xiequ.cn/VAD/GetIp.aspx?act=get&uid=157446&vkey=C8C28025450777B1E9E2F1044829C0F7&num=1&time=30&plat=0&re=1&type=0&so=1&ow=1&spl=1&addr=&db=1',
                    { dispatcher: this.directAgent }
                );

                const proxyData = await proxyResponse.json();

                if (proxyData.code === 0 && proxyData.data && proxyData.data.length > 0) {
                    const proxyInfo = proxyData.data[0];
                    const proxyUrl = `http://${proxyInfo.IP}:${proxyInfo.Port}`;
                    console.log(`获取代理: ${proxyUrl} (${proxyInfo.IpAddress})`);

                    const controller = new AbortController();
                    const timeout = setTimeout(() => {
                        controller.abort();
                    }, 10000); // 5秒超时
                    // Test the proxy
                    try {
                        this.proxyAgent = new ProxyAgent({
                            uri: proxyUrl,
                            // 头部超时（毫秒）
                            headersTimeout: 10000, //感觉没啥用处
                            // 整体请求超时（毫秒）
                            bodyTimeout: 10000, //感觉没啥用处
                        });
                        const testResponse = await fetch('https://ifconfig.me/ip', {
                            dispatcher: this.proxyAgent,
                            signal: controller.signal
                        });

                        if (testResponse.ok) {
                            const ip = await testResponse.text();
                            console.log('代理IP测试成功:', ip);
                            return proxyUrl;
                        }
                    } catch (error) {
                        console.log('代理IP测试失败:', error.message);
                    } finally {
                        clearTimeout(timeout);
                    }
                }

                retryCount++;
                console.log(`获取代理失败，第 ${retryCount} 次重试...`);
                await this.sleep(2000); // 等待2秒后重试

            } catch (error) {
                retryCount++;
                console.log(`获取代理出错，第 ${retryCount} 次重试:`, error.message);
                await this.sleep(2000);
            }
        }

        throw new Error(`获取代理IP失败，已重试 ${maxRetries} 次`);
    }

    async registerSingleAccount(account) {
        try {
            const email = account.email;
            console.log(`\n========== 开始注册账号: ${email} ==========`);

            // 发送验证码
            console.log(`[1/5] 正在发送验证码...`);
            const sendResult = await this.sendVerificationCode(email);
            if (sendResult.code !== 0) {
                throw new Error(`发送验证码失败: ${JSON.stringify(sendResult)}`);
            }
            console.log(`[1/5] ✅ 验证码发送成功`);

            // 等待邮件送达
            console.log(`[2/5] 等待邮件送达 (8秒)...`);
            await this.sleep(8000);

            // 获取验证码
            console.log(`[3/5] 正在获取验证码...`);
            const mainContent = await getEmailContent(email);
            const match = mainContent.match(/验证码为：\s*(\d+)/);
            const verificationCode = match ? match[1] : null;
            if (!verificationCode) {
                throw new Error('获取验证码失败: 未找到验证码');
            }
            console.log(`[3/5] ✅ 成功获取验证码: ${verificationCode}`);

            // 注册账号
            console.log(`[4/5] 正在注册账号...`);
            const registerResult = await this.register(email, verificationCode);
            if (registerResult.code !== 0) {
                throw new Error(`注册失败: ${JSON.stringify(registerResult)}`);
            }
            console.log(`[4/5] ✅ 账号注册成功`);

            // 登录账号
            console.log(`[5/5] 正在登录账号...`);
            const loginResult = await this.login(email);
            if (loginResult.code !== 0) {
                throw new Error(`登录失败: ${JSON.stringify(loginResult)}`);
            }
            console.log(`[5/5] ✅ 账号登录成功`);
            console.log(`Token: ${loginResult.data.token.substring(0, 50)}...`);
            console.log(`RefreshToken: ${loginResult.data.refreshToken.substring(0, 50)}...`);

            // 保存账号信息
            await this.saveAccount(email, loginResult.data);
            console.log(`✅ 账号信息已保存到 deepsider_accounts.csv`);
            console.log(`========== 账号注册完成 ==========\n`);
            return true;
        } catch (error) {
            console.error(`❌ 注册失败: ${error.message}`);
        }
        return false;
    }

    async batchRegister(accounts) {
        let successCount = 0;
        let failCount = 0;
        console.log(accounts);

        for (const account of accounts) {
            try {
                // 获取代理IP
                const proxyUrl = await this.getProxyIP();
                console.log(`使用代理: ${proxyUrl}`);
                // // 测试直连
                // try {
                //     const response = await fetch('https://ifconfig.me/ip', {
                //         dispatcher: this.directAgent // 创建新的默认 Agent 实例
                //     });
                //     const data = await response.text();
                //     console.log('API IP地址(直连):', data);
                // } catch (error) {
                //     console.log('API IP测试失败:', `${error}`);
                // }

                const success = await this.registerSingleAccount(account);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }

                // 添加随机延迟，避免请求过快
                await this.sleep(Math.random() * 2000);

            } catch (error) {
                console.error('处理账号时发生错误:', error);
                failCount++;
            }
        }

        console.log(`注册完成! 成功: ${successCount}, 失败: ${failCount}`);
    }
}

// 使用示例
async function main() {
    const register = new DeepSiderRegister();
    await register.batchRegister(accounts.slice(0, 30));
}

main().catch(console.error);
