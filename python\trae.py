import os
import re
import json
import sys
from pathlib import Path

def get_client_id(newest_log_dir):
    """获取ClientID信息"""
    main_log = newest_log_dir / "main.log"
    if not main_log.exists():
        print(f"错误: 在最新日志目录中未找到main.log文件: {newest_log_dir.name}")
        print(f"期望路径: {main_log}")
        return None
    
    try:
        with open(main_log, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"成功读取主日志文件:{main_log}")
        
        client_id_match = re.search(r'"userJwt":"{\\"ClientID\\":\\"([^\\"]*)\\', content)
        if client_id_match:
            return client_id_match.group(1)
    except Exception as e:
        print(f"错误: 读取主日志文件失败: {str(e)}")
    return None

def get_app_id(newest_log_dir):
    """获取AppID信息"""
    modulars_path = newest_log_dir / "Modular"
    if not modulars_path.exists():
        print(f"错误: 在最新日志目录中未找到Modular目录: {newest_log_dir.name}")
        print(f"期望路径: {modulars_path}")
        return None
        
    app_log = modulars_path / "ai-agent_1_stdout.log"
    if not app_log.exists():
        print(f"错误: 在最新日志目录中未找到ai-agent_1_stdout.log文件: {newest_log_dir.name}")
        print(f"期望路径: {app_log}")
        return None
        
    try:
        with open(app_log, 'r', encoding='utf-8') as f:
            app_content = f.read()
        print(f"成功读取ai-agent_1_stdout.log:{app_log}")
        app_id_match = re.search(r'"x-app-id": "([^"]*)"', app_content)
        if app_id_match:
            return app_id_match.group(1)
        else:
            print("错误: 在ai-agent_1_stdout.log中未找到APP_ID")
            print("请确认日志文件格式是否正确")
    except Exception as e:
        print(f"错误: 读取或解析ai-agent_1_stdout.log失败: {str(e)}")
    return None

def get_auth_info(app_id=None):
    """获取认证信息"""
    storage_file = Path(os.environ['USERPROFILE']) / "AppData" / "Roaming" / "Trae" / "User" / "globalStorage" / "storage.json"
    if not storage_file.exists():
        print("错误: 未找到storage.json文件")
        print(f"期望路径: {storage_file}")
        return None

    try:
        with open(storage_file, 'r', encoding='utf-8') as f:
            storage_content = f.read()
        print(f"成功读取storage.json:{storage_file}")
        
        auth_match = re.search(r'"iCubeAuthInfo://icube\.cloudide": "({.*?})"', storage_content)
        if auth_match:
            json_str = auth_match.group(1).replace('\\"', '"')
            try:
                auth_data = json.loads(json_str)
                info = {
                    'token': auth_data.get('token', ''),
                    'refresh_token': auth_data.get('refreshToken', ''),
                    'user_id': auth_data.get('userId', ''),
                    'expired_at': auth_data.get('expiredAt', ''),
                    'refresh_expired_at': auth_data.get('refreshExpiredAt', ''),
                    'username': auth_data.get('account', {}).get('username', ''),
                    'app_id': app_id
                }
                return info
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试使用正则表达式提取
                refresh_token_match = re.search(r'refreshToken":"(.*?)",".*?userId":"(\d+)"', json_str)
                if refresh_token_match:
                    return {
                        'user_id': refresh_token_match.group(2),
                        'refresh_token': refresh_token_match.group(1),
                        'app_id': app_id
                    }
                else:
                    print("错误: 无法从storage.json中提取refresh token和user ID")
                    print("请确认storage.json文件格式是否正确")
        else:
            print("错误: 在storage.json中未找到认证信息")
            print("请确认是否已登录Trae")
    except Exception as e:
        print(f"错误: 读取storage.json失败: {str(e)}")
    return None

def check_environment():
    """检查系统环境"""
    print("检查操作系统...")
    if not sys.platform.startswith('win'):
        print("错误: 该脚本仅支持Windows系统")
        print(f"当前系统: {sys.platform}")
        return None
    
    print("检查Trae安装...")
    trae_dir = Path(os.environ['USERPROFILE']) / "AppData" / "Roaming" / "Trae"
    if not trae_dir.exists():
        print("错误: 未找到Trae安装")
        print(f"期望路径: {trae_dir}")
        return None
        
    return trae_dir

def get_newest_log_dir(trae_dir):
    """获取最新的日志目录"""
    print("正在搜索日志目录...")
    try:
        log_dirs = sorted([d for d in (trae_dir / "logs").iterdir() if d.is_dir()], key=lambda x: x.name)
        if not log_dirs:
            print("错误: 未找到任何日志目录")
            print(f"日志路径: {trae_dir / 'logs'}")
            return None
        
        newest_log_dir = log_dirs[-1]
        print(f"找到最新日志目录: {newest_log_dir}")
        return newest_log_dir
    except Exception as e:
        print(f"错误: 访问日志目录时出错: {str(e)}")
        return None

def print_available_info(client_id, app_id, auth_info):
    """打印所有可用的信息"""
    print("\n==================")
    print("=== 获取结果 ===")
    if client_id:
        print(f"CLIENT_ID: {client_id}")
    if app_id:
        print(f"APP_ID: {app_id}")
    if auth_info:
        if auth_info.get('user_id'):
            print(f"USER_ID: {auth_info['user_id']}")
        if auth_info.get('refresh_token'):
            print(f"REFRESH_TOKEN: {auth_info['refresh_token']}")
            
        if auth_info.get('token'):
            print("\n=== 额外信息 ===")
            print(f"TOKEN: {auth_info['token']}")
            if auth_info.get('expired_at'):
                print(f"过期时间: {auth_info['expired_at']}")
            if auth_info.get('refresh_expired_at'):
                print(f"刷新令牌过期时间: {auth_info['refresh_expired_at']}")
            if auth_info.get('username'):
                print(f"用户名: {auth_info['username']}")
    print("==================")

def main():
    print("开始执行Trae信息提取脚本...")
    
    # 获取所有可能的信息
    trae_dir = check_environment()
    newest_log_dir = None if not trae_dir else get_newest_log_dir(trae_dir)
    
    client_id = None
    app_id = None
    auth_info = None
    
    if newest_log_dir:
        client_id = get_client_id(newest_log_dir)
        app_id = get_app_id(newest_log_dir)
        
    # 即使没有app_id也尝试获取auth_info
    auth_info = get_auth_info(app_id)
    
    # 打印所有可用信息
    print_available_info(client_id, app_id, auth_info)
    # 统计获取到的信息数量
    success_count = sum(1 for x in [client_id, app_id] if x is not None)
    success_count += 1 if auth_info and any(auth_info.values()) else 0
    total_count = 3  # ClientID, AppID, AuthInfo
    
    print(f"\n脚本执行完成! 已成功获取 {success_count}/{total_count} 项信息")

if __name__ == "__main__":
    main()
