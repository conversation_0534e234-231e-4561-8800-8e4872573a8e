import json
import logging
import asyncio
import aiohttp
from typing import List
from utils.config_manager import ConfigManager

class MicrosoftAccountCheckTask:
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.failed_accounts = []  # 存储检查失败的账号
        self.error_accounts = []  # 存储检查异常的账号

    async def check_account(self, session: aiohttp.ClientSession, email: str, max_retries: int = 10) -> bool:
        for attempt in range(max_retries):
            try:
                async with session.post(
                    'https://login.microsoftonline.com/common/GetCredentialType?mkt=zh-CN',
                    json={
                        "username": email,
                        "isOtherIdpSupported": True,
                        "checkPhones": False,
                        "isRemoteNGCSupported": True,
                        "isCookieBannerShown": False,
                        "isFidoSupported": True,
                        "originalRequest": "",
                        "country": "CN",
                        "forceotclogin": False,
                        "isExternalFederationDisallowed": False,
                        "isRemoteConnectSupported": False,
                        "federationFlags": 0,
                        "isSignup": False,
                        "flowToken": "",
                        "isAccessPassSupported": True,
                        "isQrCodePinSupported": True
                    },
                    headers={
                        "content-type": "application/json",
                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logging.info(f"检查账号 {email} 结果: {json.dumps(result)}")
                        if(result.get("ThrottleStatus") != 0):
                            raise Exception("请求频率过快")
                        if( result.get("IfExistsResult") == 1):
                            self.error_accounts.append(email)
                        return result.get("IfExistsResult") == 5
                    await asyncio.sleep(1)  # 请求失败时等待1秒后重试
            except Exception as e:
                logging.error(f"检查账号 {email} 第 {attempt + 1} 次尝试失败: {str(e)}")
                if attempt == max_retries - 1:
                    self.failed_accounts.append(email)
                await asyncio.sleep(1)  # 发生异常时等待1秒后重试
        return False

    async def do(self, accounts: List[str], max_concurrent_tasks: int = 10) -> List[str]:
        registered_accounts = []
        connector = aiohttp.TCPConnector(limit=max_concurrent_tasks)
        
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = []
            for account in accounts:
                email = account.split(":")[0]
                tasks.append(self.check_account(session, email))
                
                # 当任务数达到并发限制时执行
                if len(tasks) >= max_concurrent_tasks:
                    results = await asyncio.gather(*tasks)
                    for acc, is_registered in zip(accounts[len(registered_accounts):len(registered_accounts) + len(tasks)], results):
                        if is_registered:
                            registered_accounts.append(acc)
                    tasks = []
                    await asyncio.sleep(1)  # 添加小延迟避免请求过快
            
            # 处理剩余的任务
            if tasks:
                results = await asyncio.gather(*tasks)
                for acc, is_registered in zip(accounts[len(registered_accounts):], results):
                    if is_registered:
                        registered_accounts.append(acc)
        
        # 在返回结果前打印失败的账号
        if self.failed_accounts:
            logging.warning(f"以下账号检查失败: {json.dumps(self.failed_accounts)}")
        if self.error_accounts:
            logging.warning(f"以下账号<<可能>>没有注册(有bug可能微软尚未同步): {json.dumps(self.error_accounts)}")
            
  
        return registered_accounts
