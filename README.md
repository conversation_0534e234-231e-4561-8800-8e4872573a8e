# my-scripts

- scoop_init.ps1  重装机器后执行.

# python 相关

```bash
# 初始化环境并指定版本
uv init ./

# 创建虚拟环境并指定版本
uv venv 

# Windows 系统
.venv\Scripts\activate

# 安装依赖
uv sync 

# 生成锁文件
uv lock

# 在pyproject.tom中配置镜像
[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"

# 安装
uv add requests

# 安装到dev组中,一般是tool之类的
uv add poethepoet --dev    

# 在pyproject.tom中添加命令脚本  poe freeze  | poe dev 
[tool.poe.tasks]
dev = "python main.py"
freeze =  { shell = "uv pip compile pyproject.toml -o requirements.txt" }
```