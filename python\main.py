import asyncio
import logging
import os
import platform
from pathlib import Path
import random
import shutil
import string
from datetime import datetime
from typing import Union, Dict, Any, Optional, Tuple
from playwright.async_api import async_playwright, Play<PERSON>, <PERSON>, BrowserContext
from tasks.microsoft_verify_task import MicrosoftAccountVerifyTask
from tasks.kluster_register_task import KlusterRegisterTask
from tasks.microsoft_reward_pc_task import MicrosoftRewardPCTask
from tasks.microsoft_reward_mobile_task import MicrosoftRewardMobileTask
from utils.config_manager import ConfigManager
from tasks.microsoft_register_task import MicrosoftRegisterTask
from tasks.microsoft_account_reset_task import MicrosoftAccountResetTask
from utils.mail_verification_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from tasks.proxy_test_task import ProxyTestTask
from tasks.microsoft_died_check_task import MicrosoftDiedCheckTask
from tasks.microsoft_account_check_task import MicrosoftAccountCheckTask
from tasks.cursor_register_task import CursorRegisterTask
from tasks.github_register_task import GitHub<PERSON>egisterTask
from tasks.github_star_task import G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ask
from tasks.promptlayer_register_task import Prompt<PERSON>ayer<PERSON>eg<PERSON><PERSON>ask
from tasks.targon_register_task import TargonRegister<PERSON>ask
from utils.cache_helper import handle_route, setup_page_caching
import json
import pyotp


# kluster注册任务
async def handle_kluster_register(config: ConfigManager):
    task = KlusterRegisterTask(
        config_manager=config,
        account_file="data/kluster_accounts.csv",
        token_file="data/kluster_tokens.csv",
    )
    await task.do(10)




# Cursor注册任务
async def handle_cursor_register(playwright: Playwright, config: ConfigManager):
    task = CursorRegisterTask(
        config_manager=config, account_file="data/cursor_accounts.csv"
    )
    await task.do(playwright=playwright, register_count=1, max_concurrent_tasks=1)

 


# GitHub注册任务
async def handle_github_register(config: ConfigManager):
    task = GitHubRegisterTask(
        config_manager=config, account_file="data/github_accounts.csv"
    )
    await task.do(register_count=5, max_concurrent_tasks=1)


# GitHub点星任务
async def handle_github_star(playwright: Playwright, config: ConfigManager):
    # 获取用户输入的仓库URL
    repo_url = "https://github.com/zhezzma/hf-spaces-monitor"
    if not repo_url:
        print("错误: 仓库URL不能为空!")
        return

    # 验证URL格式
    if not repo_url.startswith("https://github.com/"):
        print("错误: 请输入有效的GitHub仓库URL (以 https://github.com/ 开头)")
        return

    max_concurrent = 1

    account_limit = 50

    print(f"开始为仓库点星: {repo_url}")
    print(f"最大并发数: {max_concurrent}")
    print(f"账号数量限制: {account_limit if account_limit else '无限制'}")

    task = GitHubStarTask(
        config_manager=config,
        account_file="data/github_accounts.csv",
        error_file="data/github_star_error_accounts.txt"
    )

    result = await task.do(
        playwright=playwright,
        repo_url=repo_url,
        max_concurrent_tasks=max_concurrent,
        account_limit=account_limit
    )

    print(f"\n点星任务完成!")
    print(f"成功: {result['success']}")
    print(f"失败: {result['failed']}")
    print(f"总计: {result['total']}")

    if result['failed'] > 0:
        print(f"失败的账号已记录到: data/github_star_error_accounts.txt")


# PromptLayer注册任务
async def handle_promptlayer_register(playwright: Playwright, config: ConfigManager):
    task = PromptLayerRegisterTask(
        config_manager=config,
        account_file="data/promptlayer.csv",
        error_file="data/promptlayer_error_accounts.txt",
    )
    await task.do(playwright=playwright, register_count=5, max_concurrent_tasks=1)


# Targon注册任务
async def handle_targon_register(config: ConfigManager):
    task = TargonRegisterTask(
        account_file="data/targon_accounts.csv",
        error_file="data/targon_error_accounts.txt"
    )
    await task.do(register_count=500)


# 微软注册任务
async def handle_microsoft_register(playwright: Playwright, config: ConfigManager):
    task = MicrosoftRegisterTask(
        config_manager=config, account_file="data/microsoft_accounts.csv"
    )
    await task.do(playwright=playwright, max_concurrent_tasks=10)


# 微软奖励桌面端任务
async def handle_microsoft_reward_pc(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_reward.json")
    if not accounts:
        return
    task = MicrosoftRewardPCTask(config_manager=config)
    failed_accounts = await task.do(playwright, accounts)
    save_json(failed_accounts, "data/microsoft_reward_pc_faild_accounts.json")


# 微软奖励移动端任务
async def handle_microsoft_reward_mobile(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_reward.json")
    if not accounts:
        return
    task = MicrosoftRewardMobileTask(config_manager=config)
    success_accounts, failed_accounts = await task.do(playwright, accounts, 3)
    save_json(success_accounts, "data/microsoft_reward_mobile_success_accounts.json")
    save_json(failed_accounts, "data/microsoft_reward_mobile_failed_accounts.json")


# 处理微软绑定邮箱和修改密码任务
async def handle_miscrosft_reset(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_reset.txt")
    if not accounts:
        return
    task = MicrosoftAccountResetTask(config_manager=config)
    success_accounts, failed_accounts = await task.do(
        playwright, accounts, 3
    )  # 修改接收返回值
    save_text(failed_accounts, "data/microsoft_reset_failed_accounts.txt")
    save_text(
        success_accounts, "data/microsoft_reset_success_accounts.txt"
    )  # 保存成功账号


# 微软验证任务和绑定邮箱,使用手机验证解决被禁用的账号
async def handle_microsoft_verify(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_verify.txt")
    if not accounts:
        return
    task = MicrosoftAccountVerifyTask(config_manager=config, account_unlock=False)
    success_accounts, failed_accounts = await task.do(playwright, accounts, 10)
    save_text(failed_accounts, "data/microsoft_verify_faild_accounts.txt")
    save_text(success_accounts, "data/microsoft_verify_success_accounts.txt")


# 微软账号死号检查任务
async def handle_microsoft_died_check(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_died_check.json")
    if not accounts:
        return
    task = MicrosoftDiedCheckTask(config_manager=config)
    active_accounts, failed_accounts = await task.do(playwright, accounts, 10)
    if active_accounts:
        save_json(active_accounts, "data/microsoft_died_recovery.json")
    if failed_accounts:
        save_json(failed_accounts, "data/microsoft_died_failed.json")


# 代理测试任务
async def handle_proxy_test(playwright: Playwright, config: ConfigManager):
    # 读取原始代理数据
    proxy_lines = read_accounts_from_file("data/proxy_raw.txt")
    if not proxy_lines:
        logging.error("proxy_raw.txt not found or empty!")
        print("错误: 找不到 proxy_raw.txt 文件或文件为空!")
        return

    logging.info(f"Loaded {len(proxy_lines)} proxies from file")
    print(f"从文件中加载了 {len(proxy_lines)} 个代理")

    # 创建任务实例并测试代理
    task = ProxyTestTask(config_manager=config, default_protocol="http")
    working_proxies = await task.test_proxies(proxy_lines, max_concurrent=50)

    # 保存可用代理到JSON文件
    if working_proxies:
        save_json(working_proxies, "data/proxy_succeed.json")
        logging.info(
            f"Successfully saved {len(working_proxies)} working proxies to proxy_succeed.json"
        )
    else:
        logging.warning("No working proxies found!")


# 微软死号去重任务
def handle_microsoft_died_deduplicate(config: ConfigManager):
    # 读取microsoft_died.json文件
    filename = "data/microsoft_died.json"
    accounts = read_accounts_from_file(filename)
    if not accounts:
        logging.error(f"{filename} file not found or empty!")
        print(f"错误: 找不到 {filename} 文件或文件为空!")
        return

    original_count = len(accounts)
    logging.info(f"Loaded {original_count} accounts from {filename}")
    print(f"从 {filename} 中加载了 {original_count} 个账号")

    # 创建备份
    backup_filename = f"{filename}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
    try:
        shutil.copy2(filename, backup_filename)
        logging.info(f"Created backup at {backup_filename}")
        print(f"已创建备份文件: {backup_filename}")
    except Exception as e:
        logging.error(f"Error creating backup: {str(e)}")
        print(f"创建备份时出错: {str(e)}")
        return

    # 根据email去重
    unique_accounts = {}
    for account in accounts:
        if "email" in account and account["email"]:
            email = account["email"].lower()  # 转为小写以确保不区分大小写
            if email not in unique_accounts:
                unique_accounts[email] = account

    # 转换回列表
    deduplicated_accounts = list(unique_accounts.values())
    deduplicated_count = len(deduplicated_accounts)

    # 保存去重后的数据
    try:
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(deduplicated_accounts, f, indent=4, ensure_ascii=False)
        logging.info(f"Saved {deduplicated_count} deduplicated accounts to {filename}")
        print(f"已保存 {deduplicated_count} 个去重后的账号到 {filename}")
        print(f"去除了 {original_count - deduplicated_count} 个重复账号")
    except Exception as e:
        logging.error(f"Error saving deduplicated accounts: {str(e)}")
        print(f"保存去重账号时出错: {str(e)}")


# 微软账号检查任务
async def handle_microsoft_account_check(playwright: Playwright, config: ConfigManager):
    accounts = read_accounts_from_file("data/microsoft_emails.txt")
    if not accounts:
        return
    task = MicrosoftAccountCheckTask(config_manager=config)
    registered_accounts = await task.do(
        accounts, 8
    )  # 增加并发数到20,移除playwright参数
    if registered_accounts:
        save_text(registered_accounts, "data/microsoft_account_check_success.txt")
        print(f"已注册的账号已保存到 data/microsoft_account_check_success.txt")


# 转换为:格式
def convert_to_reset_format(config: ConfigManager):
    # 检查 microsoft_emails.txt 是否存在且为空
    try:
        if Path("data/microsoft_emails.txt").exists():
            with open("data/microsoft_emails.txt", "r", encoding="utf-8") as f:
                if f.read().strip():
                    print("data/microsoft_emails.txt 文件不为空，请先清空文件内容！")
                    return
    except Exception as e:
        logging.error(f"检查文件时出错: {str(e)}")
        return

    accounts = read_accounts_from_file("data/microsoft_accounts.csv")
    if not accounts:
        return

    try:
        # 处理账户数据并转换格式
        formatted_accounts = []
        for account in accounts:
            if isinstance(account, str) and "," in account:
                parts = account.strip().split(",")
                if len(parts) >= 2:
                    email = parts[0].strip()
                    password = parts[1].strip()
                    formatted_accounts.append(f"{email}:{password}")

        # 写入转换后的格式
        if formatted_accounts:
            with open("data/microsoft_emails.txt", "w", encoding="utf-8") as f:
                f.write("\n".join(formatted_accounts))
            print("\n格式转换完成！数据已保存至 data/microsoft_emails.txt")
        else:
            print("没有找到有效的账户数据！")

    except Exception as e:
        logging.error(f"转换过程中出错: {str(e)}")


# TOTP验证码生成任务
def handle_totp_generate(config: ConfigManager):
    """
    使用pyotp根据手动码生成TOTP验证码

    原理说明:
    TOTP (Time-based One-Time Password) 是基于时间的一次性密码算法。
    它使用一个共享的密钥(手动码/secret key)和当前时间来生成6位数字验证码。

    工作原理:
    1. 服务端和客户端共享一个密钥(手动码)
    2. 算法基于当前时间(通常以30秒为间隔)和密钥生成验证码
    3. 每30秒验证码会自动更新
    4. 服务端验证时会检查当前时间窗口内的验证码是否匹配

    关于手动码:
    - 每个账号都有唯一的手动码(secret key)
    - 手动码通常在启用2FA时由服务提供商生成
    - 手动码是Base32编码的字符串，如: JBSWY3DPEHPK3PXP
    - 同一个手动码在相同时间总是生成相同的验证码
    - 不同账号的手动码不同，因此生成的验证码也不同
    """
    print("=== TOTP验证码生成器 ===")
    print("请输入手动码来生成TOTP验证码")
    print()

    # 单个手动码输入
    manual_code = input("请输入手动码(Secret Key): ").strip()
    if not manual_code:
        print("错误: 手动码不能为空!")
        return

    try:
        # 创建TOTP对象
        totp = pyotp.TOTP(manual_code)
        # 生成当前验证码
        current_code = totp.now()
        # 获取剩余有效时间
        remaining_time = 30 - (int(datetime.now().timestamp()) % 30)

        print(f"\n生成结果:")
        print(f"手动码: {manual_code}")
        print(f"当前验证码: {current_code}")
        print(f"剩余有效时间: {remaining_time}秒")
        print(f"下一个验证码将在 {remaining_time} 秒后生成")

    except Exception as e:
        print(f"错误: 生成验证码失败 - {str(e)}")
        print("请检查手动码格式是否正确")


# 转换microsoft_emails_will_use.txt为json格式
def convert_emails_will_use_to_json(config: ConfigManager):
    """
    将 microsoft_emails_will_use.txt 中的账号密码转换为 JSON 数组格式
    """
    input_file = "data/microsoft_emails_will_use.txt"
    output_file = "data/microsoft_emails_will_use.json"

    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误：找不到文件 {input_file}")
        return

    accounts = []

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                # 分割邮箱和密码
                if ':' in line:
                    email, password = line.split(':', 1)  # 使用 split(':', 1) 防止密码中包含冒号
                    accounts.append({
                        "email": email.strip(),
                        "password": password.strip()
                    })
                else:
                    print(f"警告：第 {line_num} 行格式不正确，跳过: {line}")

        # 将结果写入 JSON 文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)

        print(f"转换完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"共转换了 {len(accounts)} 个账号")

        # 显示前几个账号作为示例
        if accounts:
            print("\n前3个账号示例:")
            for i, account in enumerate(accounts[:3], 1):
                print(f"{i}. {account}")

    except Exception as e:
        print(f"转换过程中出现错误: {e}")


# 转换为json格式
def convert_to_json_format(config: ConfigManager):
    # 检查 microsoft_reward.json 是否存在且为空
    try:
        if Path("data/microsoft_reward.json").exists():
            with open("data/microsoft_reward.json", "r", encoding="utf-8") as f:
                if f.read().strip():
                    print("data/microsoft_reward.json 文件不为空，请先清空文件内容！")
                    return
    except Exception as e:
        logging.error(f"检查文件时出错: {str(e)}")
        return

    accounts = read_accounts_from_file("data/microsoft_emails.txt")
    if not accounts:
        return
    # 添加是否使用新密码的选项
    use_new_password = input("是否使用新密码 (y/n): ").strip().lower() == "y"

    proof_email_config = config.proof_email[0]
    proof_domain = proof_email_config["domain"]
    rewards_json = []

    for account in accounts:
        if not account.strip():
            continue

        # 处理 account 格式 (如 <EMAIL>:C5AcXbsCni)
        account_parts = account.strip().split(":")
        email = account_parts[0]

        # 确定使用哪个密码
        if use_new_password:
            password = config.user_new_password
        else:
            # 如果有原始密码就用原始密码，否则使用新密码
            password = (
                account_parts[1] if len(account_parts) > 1 else config.user_new_password
            )

        rewards_json.append(
            {
                "email": email.lower(),
                "password": password,
                "proofEmail": f"{email.split('@')[0].lower()}@{proof_domain}",
            }
        )

    with open("data/microsoft_reward.json", "w", encoding="utf-8") as f:
        json.dump(rewards_json, f, indent=4, ensure_ascii=False)
    print("\nJSON格式转换完成！数据已保存至 data/microsoft_reward.json")


# 从文件中读取账户数据
def read_accounts_from_file(filename):
    try:
        with open(filename, "r", encoding="utf-8") as f:
            if "[" in f.readline():  # 检查是否是JSON格式
                f.seek(0)  # 重置文件指针到开始
                return json.load(f)
            else:
                f.seek(0)
                return [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logging.error(f"{filename} file not found!")
    except Exception as e:
        logging.error(f"Error reading {filename}: {str(e)}")
    return None


# 处理失败的账户
def save_text(failed_accounts, filename):
    if failed_accounts:
        logging.info(f"accounts have been saved to {filename}")
        logging.info(f"Total accounts: {len(failed_accounts)}")
        with open(filename, "a", encoding="utf-8") as f:
            for account in failed_accounts:
                f.write(f"{account}\n")


# 处理失败的账户 (JSON格式)
def save_json(failed_accounts, filename):
    if failed_accounts:
        logging.info(f"accounts have been saved to {filename}")
        logging.info(f"Total accounts: {len(failed_accounts)}")
        try:
            # 如果文件已存在，先读取现有内容
            existing_accounts = []
            if Path(filename).exists():
                with open(filename, "r", encoding="utf-8") as f:
                    try:
                        existing_accounts = json.load(f)
                    except json.JSONDecodeError:
                        pass

            # 合并现有账户和新的失败账户
            all_accounts = existing_accounts + failed_accounts

            # 写入所有账户
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(all_accounts, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Error saving accounts to {filename}: {str(e)}")


 
async def main():
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler("app.log"), logging.StreamHandler()],
    )
    config = ConfigManager(
        env_file=".env", config_files=["config.json"], auto_reload=True
    )
    # 初始化 VerificationHelper
    MailVerificationHelper.init(config.email_api_key)

    task_options = {
        "1": ("微软奖励移动端初始化", handle_microsoft_reward_mobile),
        "2": ("微软奖励桌面端初始化", handle_microsoft_reward_pc),
        "3": ("微软绑定邮箱和修改密码", handle_miscrosft_reset),
        "4": ("微软注册", handle_microsoft_register),
        "5": ("手机号验证禁用账号和绑定邮箱", handle_microsoft_verify),
        "6": ("检查微软死号", handle_microsoft_died_check),
        "7": ("检查微软账号是否注册", handle_microsoft_account_check),
        "8": ("微软死号去重", handle_microsoft_died_deduplicate),  # 新增选项
        "9": ("TOTP验证码生成器", handle_totp_generate),  # 新增TOTP任务
        "50": ("microsoft_emails.txt=>microsoft_reward.json", convert_to_json_format),
        "51": ("microsoft_accounts.csv=>microsoft_emails.txt", convert_to_reset_format),
        "52": ("microsoft_emails_will_use.txt=>microsoft_emails_will_use.json", convert_emails_will_use_to_json),
        "101": ("kluster注册", handle_kluster_register),
        "103": ("Cursor注册", handle_cursor_register),
        "105": ("GitHub注册", handle_github_register),
        "106": ("PromptLayer注册", handle_promptlayer_register),
        "107": ("GitHub项目点星", handle_github_star),
        "108": ("Targon注册", handle_targon_register),
        "201": ("测试代理并转换格式", handle_proxy_test),
    }

    print("请选择任务类型：")
    for key, (description, _) in task_options.items():
        print(f"{key}.{description}")

    task_type = input("请输入任务编号：\n")

    if task_type not in task_options:
        logging.error("Invalid task type!")
        return
    if task_type in ["8", "9", "50", "51", "52"]:
        task_options[task_type][1](config)
        return
    if task_type in ["101", "105", "108"]:
        await task_options[task_type][1](config)
        return
    async with async_playwright() as p:
        await task_options[task_type][1](p, config)


if __name__ == "__main__":
    asyncio.run(main())
