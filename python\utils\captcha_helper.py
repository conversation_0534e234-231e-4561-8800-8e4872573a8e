import asyncio
import json
import logging
import time
import requests
from twocaptcha import TwoCaptcha
import aiohttp


class CaptchaHelper:
    """验证码处理帮助类"""
    
    def __init__(self, two_captcha_key: str, captcharun_token: str = None, scrapeless_api_key: str = None,browser_user_agent:str=None) -> None:
        self.logger = logging.getLogger(__name__)
        self.two_captcha_key = two_captcha_key
        self.two_captcha = TwoCaptcha(two_captcha_key, defaultTimeout=600)
        self.captcharun_token = captcharun_token
        self.scrapeless_api_key = scrapeless_api_key
        self.browser_user_agent = browser_user_agent or "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"


    async def get_two_captcha_token_v2(self,site_key: str, blob: str, surl: str,siteReferer:str, max_retries=30, retry_interval=5000):
   
        try:
            # 第一步：创建验证码任务
            async with aiohttp.ClientSession() as session:
                create_task_response = await session.post(
                    'https://api.2captcha.com/createTask',
                    headers={
                        'Content-Type': 'application/json',
                    },
                    json={
                        'clientKey': self.two_captcha_key,
                        'task': {
                            'type': 'FunCaptchaTaskProxyless',
                            'websiteURL': siteReferer,
                            'websitePublicKey': site_key,
                            'funcaptchaApiJSSubdomain': surl,  # "client-api.arkoselabs.com",
                            "userAgent": self.browser_user_agent,
                            "data": json.dumps({"blob": blob})
                        },
                    }
                )
                
                create_task_result = await create_task_response.json()
                
                if create_task_result.get('errorId') != 0:
                    raise Exception(f"创建任务失败: {json.dumps(create_task_result)}")
                
                task_id = create_task_result['taskId']
                self.logger.info(f"识别任务创建成功,等待任务完成中...{task_id}")
                # 第二步：获取任务结果（带重试逻辑）
                retries = 0
                
                while retries < max_retries:
                    # 等待指定的间隔时间
                    await asyncio.sleep(retry_interval / 1000)  # 转换为秒
                    
                    get_result_response = await session.post(
                        'https://api.2captcha.com/getTaskResult',
                        headers={
                            'Content-Type': 'application/json',
                        },
                        json={
                            'clientKey': self.two_captcha_key,
                            'taskId': task_id,
                        }
                    )
                    
                    get_result_data = await get_result_response.json()

                    
                    if get_result_data.get('errorId') != 0:
                        raise Exception(f"获取任务结果失败: {json.dumps(get_result_data)}")
                    
                    # 检查任务是否完成
                    if get_result_data.get('status') == 'ready':
                        self.logger.info(f"识别成功:{json.dumps(get_result_data)}")
                        # 返回验证码令牌
                        return get_result_data['solution']['token']
                    
                    # 如果任务仍在处理中，增加重试次数
                    retries += 1
                
                raise Exception(f"超过最大重试次数({max_retries})，无法获取验证码结果")
        except Exception as error:
            raise Exception(f"解决失败: {str(error)}")

    def get_two_captcha_token(self, site_key: str, blob: str, surl: str,siteReferer:str, timeout: int = 180, interval: int = 3) -> str:
        """使用2captcha服务获取token"""
        try:
            solver_result = self.two_captcha.funcaptcha(
                sitekey=site_key,
                url=siteReferer,
                data=json.dumps({"blob": blob}),
                userAgent=self.browser_user_agent,
                surl=surl
            )
            self.logger.info(f"识别成功:{str(solver_result)}")
            return solver_result['code']
        except Exception as e:
            raise Exception(f"获取验证码失败: {str(e)}")

    def get_captcharun_token(self, site_key: str, blob: str, surl: str ,siteReferer:str, timeout: int = 180, interval: int = 3) -> str:
        """使用captcharun服务获取token"""
        headers = {'Authorization': f'Bearer {self.captcharun_token}'}
        response = requests.post("https://api.captcha.run/v2/tasks", headers=headers, json={
            "captchaType": "FunCaptcha",
            "siteReferer": siteReferer,
            "siteKey": site_key,
            "funcaptchaApiJSSubdomain": surl,
            "userAgent": self.browser_user_agent,
            "data": blob
        })
        response.raise_for_status()
        result = response.json()
        task_id = result.get("taskId")
        if task_id is None:
            raise Exception("创建任务失败")
        self.logger.info(f"识别任务创建成功,等待任务完成中...{task_id}")
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f'https://api.captcha.run/v2/tasks/{task_id}', headers=headers)
                response.raise_for_status()
                result = response.json()
                solution = result.get('response', {})
                if result.get("status", "Fail") == "Fail":
                    raise Exception("识别失败")
                if solution and 'token' in solution:
                    self.logger.info(f"识别成功:{str(solution)}")
                    return solution['token']
                time.sleep(interval)
            except requests.exceptions.RequestException as e:
                self.logger.error(f"获取任务结果出错: {e}")
                time.sleep(interval)
            except KeyError:
                raise Exception(f"服务器返回的响应无效: {result}")
        raise Exception("获取验证码超时")

    async def get_turnstile_token(self, page_url: str, site_key: str) -> str:
        """获取Cloudflare Turnstile token"""
        url = "https://api.scrapeless.com/api/v1/createTask"
        headers = {"x-api-token": self.scrapeless_api_key}
        input_data = {
            "version": "{{version}}",
            "pageURL": page_url,
            "siteKey": site_key,
            "pageAction": "SIGNUP",
            "invisible": True,
        }
        payload = {
            "actor": "captcha.turnstile",
            "input": input_data
        }

        return await self._get_scrapeless_token(url, headers, payload)

    async def get_recaptcha_token(self, page_url: str, site_key: str) -> str:
        """获取Google ReCaptcha token"""
        url = "https://api.scrapeless.com/api/v1/createTask"
        headers = {"x-api-token": self.scrapeless_api_key}
        input_data = {
            "version": "v2",
            "pageURL": page_url,
            "siteKey": site_key,
            "pageAction": "SIGNUP",
            "invisible": True,
        }
        payload = {
            "actor": "captcha.recaptcha",
            "input": input_data
        }

        return await self._get_scrapeless_token(url, headers, payload)

    async def _get_scrapeless_token(self, url: str, headers: dict, payload: dict) -> str:
        """通用的scrapeless token获取方法"""
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                result = await response.json()
                task_id = result.get("taskId")
                if not task_id:
                    raise Exception("Failed to create captcha task")

            self.logger.info(f"Created captcha task: {task_id}")

            for _ in range(10):
                await asyncio.sleep(1)
                check_url = f"https://api.scrapeless.com/api/v1/getTaskResult/{task_id}"
                async with session.get(check_url, headers=headers) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        if result.get("success"):
                            return result["solution"]["token"]

            raise Exception("Failed to get captcha token")
