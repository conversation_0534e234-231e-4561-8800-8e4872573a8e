import { accounts, getEmailContent } from './utils/msmail.js';
import fs from 'fs/promises';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
import { getProxyAgent } from './utils/proxy.js';

const password = "Dean0104@duple";
const CSV_FILE_PATH = 'data/duple.csv';
const ERROR_FILE_PATH = 'data/duple_error_accounts.txt';
let directAgent = new Agent();
let proxyAgent = directAgent;

/**
 * 读取已经注册的账号，避免重复注册
 * @returns {Promise<Set<string>>} 已注册的邮箱集合
 */
async function getRegisteredEmails() {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        // 检查CSV文件是否存在
        try {
            await fs.access(CSV_FILE_PATH);
        } catch (error) {
            // 如果文件不存在，创建一个空文件
            await fs.writeFile(CSV_FILE_PATH, '');
            return new Set();
        }

        // 读取并解析CSV文件
        const content = await fs.readFile(CSV_FILE_PATH, 'utf-8');
        const lines = content.trim().split('\n');

        // 提取邮箱（CSV的第一列）
        const emails = new Set();
        for (const line of lines) {
            if (line.trim()) {
                const [email] = line.split(',');
                emails.add(email.trim());
            }
        }

        console.log(`已从${CSV_FILE_PATH}读取${emails.size}个已注册账号`);
        return emails;
    } catch (error) {
        console.error(`读取已注册账号时出错: ${error.message}`);
        return new Set();
    }
}

/**
 * 读取注册失败的错误账号，避免重复注册
 * @returns {Promise<string[]>} 注册失败的邮箱数组
 */
async function getErrorEmails() {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        // 检查错误账号文件是否存在
        try {
            await fs.access(ERROR_FILE_PATH);
        } catch (error) {
            // 如果文件不存在，创建一个空文件
            await fs.writeFile(ERROR_FILE_PATH, '');
            return [];
        }

        // 读取并解析文件
        const content = await fs.readFile(ERROR_FILE_PATH, 'utf-8');
        const emails = content.trim().split('\n')
            .filter(line => line.trim())
            .map(line => line.trim());

        console.log(`已从${ERROR_FILE_PATH}读取${emails.length}个注册失败的账号`);
        return emails;
    } catch (error) {
        console.error(`读取注册失败账号时出错: ${error.message}`);
        return [];
    }
}

/**
 * 保存注册失败的账号到文件
 * @param {string} email 注册失败的邮箱
 */
async function saveErrorAccount(email) {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        await fs.appendFile(ERROR_FILE_PATH, email + '\n');
        console.log(`成功保存注册失败账号 ${email} 到 ${ERROR_FILE_PATH}`);
    } catch (error) {
        console.error(`保存注册失败账号时出错: ${error.message}`);
    }
}

/**
 * 保存账号信息到CSV文件
 * @param {string} email 邮箱
 * @param {string} status 注册状态
 */
async function saveAccount(email, status = 'registered') {
    try {
        const data = `${email},${status}\n`;
        await fs.appendFile(CSV_FILE_PATH, data);
        console.log(`成功保存账号 ${email} 到 ${CSV_FILE_PATH}`);
    } catch (error) {
        console.error(`保存账号时出错: ${error.message}`);
    }
}

/**
 * 创建Duple账号
 * @param {string} email 邮箱
 * @param {string} name 用户名
 * @returns {Promise<object>} 注册结果
 */
async function createAccount(email, name) {
    try {
        const response = await fetch("https://spfbvvhxgnohlebgjsfo.supabase.co/auth/v1/signup?redirect_to=https%3A%2F%2Fwebapp.duple.ai%2F", {
            method: "POST",
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Xlo-SGp-AkKEV-nOyEsUbf8X6avKTZ2EGqHr12-1Ppk",
                "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Xlo-SGp-AkKEV-nOyEsUbf8X6avKTZ2EGqHr12-1Ppk",
                "cache-control": "no-cache",
                "content-type": "application/json;charset=UTF-8",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "cross-site",
                "x-client-info": "supabase-js-web/2.49.10",
                "x-supabase-api-version": "2024-01-01",
                "Referer": "https://webapp.duple.ai/"
            },
            body: JSON.stringify({
                email: email,
                password: password,
                data: { name: name },
                gotrue_meta_security: {},
                code_challenge: null,
                code_challenge_method: null
            }),
            dispatcher: proxyAgent
        });

        if (!response.ok) {
            throw new Error(`创建账号失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`成功创建账号: ${email}`);
        return result;
    } catch (error) {
        console.error(`创建账号时出错: ${error.message}`);
        throw error;
    }
}

/**
 * 从邮件内容中提取确认链接
 * @param {string} emailContent 邮件内容
 * @returns {string|null} 确认链接
 */
function extractConfirmationLink(emailContent) {
    // 匹配HTML中的href属性的正则表达式
    const linkPattern = /href="(https:\/\/spfbvvhxgnohlebgjsfo\.supabase\.co\/auth\/v1\/verify\?[^"]+)"/;
    const match = emailContent.match(linkPattern);

    if (match) {
        // 清理链接中的HTML编码字符
        let link = match[1];
        link = link.replace(/&amp;/g, '&');
        link = link.replace(/=3D/g, '=');
        return link;
    }

    return null;
}

/**
 * 确认邮箱
 * @param {string} confirmationLink 确认链接
 * @returns {Promise<boolean>} 确认是否成功
 */
async function confirmEmail(confirmationLink) {
    try {
        const response = await fetch(confirmationLink, {
            method: "GET",
            headers: {
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "pragma": "no-cache",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "cross-site",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1"
            },
            dispatcher: proxyAgent
        });

        if (response.ok) {
            console.log('邮箱确认成功');
            return true;
        } else {
            console.error(`邮箱确认失败: ${response.status} ${response.statusText}`);
            return false;
        }
    } catch (error) {
        console.error(`邮箱确认时出错: ${error.message}`);
        return false;
    }
}

/**
 * 等待并获取确认邮件
 * @param {string} email 邮箱地址
 * @param {number} maxRetries 最大重试次数
 * @returns {Promise<string|null>} 确认链接
 */
async function waitForConfirmationEmail(email, maxRetries = 30) {
    console.log(`等待确认邮件: ${email}`);

    for (let i = 0; i < maxRetries; i++) {
        try {
            const emailContent = await getEmailContent(email, true);
            console.log(`第${i + 1}次获取邮件内容`);

            if (emailContent && (emailContent.includes('Confirm your signup') || emailContent.includes('confirm your user'))) {
                const confirmationLink = extractConfirmationLink(emailContent);
                if (confirmationLink) {
                    console.log(`找到确认链接: ${confirmationLink}`);
                    return confirmationLink;
                } else {
                    console.log('邮件中未找到有效的确认链接');
                }
            } else {
                console.log('未收到确认邮件或邮件内容不匹配');
            }
        } catch (error) {
            console.log(`第${i + 1}次获取邮件失败: ${error.message}`);
        }

        // 等待3秒后重试
        console.log(`等待3秒后进行第${i + 2}次尝试...`);
        await new Promise(resolve => setTimeout(resolve, 3000));
    }

    console.error(`等待确认邮件超时: ${email}`);
    return null;
}

/**
 * 注册Duple账号的完整流程
 * @param {object} account 账号信息
 */
async function registerDuple(account) {
    try {
        const email = account.email;
        // 使用邮箱前缀作为用户名
        const name = email.split('@')[0];
        
        // 创建账号
        console.log(`开始注册账号: ${email}`);
        const accountResult = await createAccount(email, name);
        
        // 等待确认邮件
        const confirmationLink = await waitForConfirmationEmail(email);
        if (!confirmationLink) {
            throw new Error('未收到确认邮件');
        }
        
        // 确认邮箱
        const confirmed = await confirmEmail(confirmationLink);
        if (!confirmed) {
            throw new Error('邮箱确认失败');
        }
        
        // 保存账号信息
        await saveAccount(email, 'confirmed');
        
        console.log(`账号注册完成: ${email}`);
        return { email, status: 'confirmed' };
    } catch (error) {
        console.error(`注册账号 ${account.email} 失败: ${error.message}`);
        // 保存错误账号到文件
        await saveErrorAccount(account.email);
        return null;
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        console.log('开始Duple账号注册流程...');

        // 获取已注册的账号
        const registeredEmails = await getRegisteredEmails();
        // 获取注册失败的账号
        const errorEmails = await getErrorEmails();

        // 过滤出未注册的账号且不在错误邮箱列表中的账号
        const unregisteredAccounts = accounts.filter(account =>
            !registeredEmails.has(account.email) &&
            !errorEmails.includes(account.email)
        );
        console.log(`找到${unregisteredAccounts.length}个未注册的账号`);

        if (unregisteredAccounts.length === 0) {
            console.log('没有需要注册的账号');
            return;
        }

        // 注册账号
        const results = [];
        for (const account of unregisteredAccounts) {
            console.log(`正在注册账号: ${account.email}`);
            const result = await registerDuple(account);
            if (result) {
                results.push(result);
            }
            // 添加延迟，避免请求过快
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log(`注册完成，成功注册${results.length}个账号`);
    } catch (error) {
        console.error(`注册流程出错: ${error.message}`);
    }
}

// 运行主函数
main().catch(console.error);
