import { Agent } from 'undici';
import { readFileSync } from 'fs';
import { join } from 'path';

export const mailHeaders = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "authorization": "Bearer 9ba4c11ce3a9235a34b3481795485dec",
    "content-type": "application/json"
};

// 读取并解析 microsoft_died.json 文件
const accountsData = readFileSync(join(process.cwd(), 'data', 'microsoft_died.json'), 'utf8');
export const accounts = JSON.parse(accountsData);

export async function getEmailContent(email, html = false) {
    const directAgent = new Agent();
    const response = await fetch("https://godmailyi-msmail.hf.space/api/mail/new", {
        method: "POST",
        headers: mailHeaders,
        body: JSON.stringify({ email: email }),
        dispatcher: directAgent
    });
    if (!response.ok) {
        const data = await response.json();
        throw new Error(`获取验证码失败: ${data.error}`);
    }
    const data = await response.json();

    if (html) {
        return data.html;
    }
    return data.text;
}
