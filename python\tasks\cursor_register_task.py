import json
import logging
import os
from pathlib import Path
import signal
import sys
import time
import random
import re
from typing import Union, List, Tuple, Optional
import aiofiles
import requests
from twocaptcha import TwoCaptcha
from utils.cache_helper import handle_route, setup_page_caching
from utils.config_manager import ConfigManager
from utils.user_generator import User, UserGenerator
from playwright.async_api import async_playwright, Playwright, Page, Frame, Request, Route
from urllib.parse import urlparse, parse_qs
import asyncio
from typing import List, Dict, Optional
from utils.captcha_helper import CaptchaHelper
import aiohttp

# 定义自定义异常类
class IPBadError(Exception):
    """自定义的异常类"""
    def __init__(self, message="IPBadError"):
        self.message = message
        super().__init__(self.message)
    def __str__(self):
        return f"IPBadError: {self.message}"

class CursorRegisterTask:
    """
    Cursor注册任务
    """
    def __init__(
        self,
        config_manager: Config<PERSON>anager,
        account_file: Union[str, Path] = "cursor_accounts.csv"
    ) -> None:
        """
        初始化配置管理器

        Args:
            config_manager: 配置管理
            account_file: 账号文件
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.user_generator = UserGenerator(config_manager)
        self.two_captcha = TwoCaptcha(self.config_manager.two_captcha_key, defaultTimeout=600)
        self.browserUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.file_lock = asyncio.Lock()
        self.captcha_helper = CaptchaHelper(
            two_captcha_key=self.config_manager.two_captcha_key,
            captcharun_token=self.config_manager.captcharun_token,
            browser_user_agent=self.browserUserAgent
        )


    async def _handle_turnstile(self, page: Page) -> bool:
        """处理Turnstile验证"""
        self.logger.info("=============正在检测 Turnstile 验证=============")
        try:
            max_attempts = 5
            attempts = 0
            while attempts < max_attempts:
                try:
                    # 检查页面状态，但不直接返回，先检查是否有Turnstile验证需要处理
                    page_ready = False
                    if await page.is_visible('input[name="password"]', timeout=1000):
                        self.logger.info("检测到密码输入页面，检查是否有验证需要处理...")
                        page_ready = True
                    elif await page.is_visible('[data-index="0"]', timeout=1000):
                        self.logger.info("检测到验证码输入页面，检查是否有验证需要处理...")
                        page_ready = True
                    elif await page.is_visible("text=Account Settings", timeout=1000):
                        self.logger.info("检测到账户设置页面，检查是否有验证需要处理...")
                        page_ready = True

                    # 即使页面已经准备好，也检查是否有Turnstile验证需要处理
                    self.logger.info("检测 Turnstile 验证...")
                    try:
                        # 使用更精确的选择器定位Turnstile验证元素
                        # 首先获取cf-turnstile元素
                        cf_turnstile = await page.query_selector('#cf-turnstile', timeout=2000)
                        if cf_turnstile:
                            # 获取其子元素
                            child = await cf_turnstile.query_selector(':scope > *')
                            if child:
                                # 获取shadow root
                                shadow_root = await child.evaluate_handle('el => el.shadowRoot')
                                if shadow_root:
                                    # 在shadow root中查找iframe
                                    iframe = await shadow_root.evaluate_handle('root => root.querySelector("iframe")')
                                    if iframe:
                                        # 在iframe中查找body和input元素
                                        self.logger.info("检测到 Turnstile 验证，正在处理...")
                                        # 点击iframe以触发验证
                                        await iframe.as_element().click()
                                        await page.wait_for_timeout(random.uniform(1000, 3000))
                                        self.logger.info("Turnstile 验证通过")
                                        return True
                    except Exception as e:
                        self.logger.debug(f"Turnstile iframe检测异常: {str(e)}")

                    # 如果页面已准备好且没有验证需要处理，则可以返回
                    if page_ready:
                        self.logger.info("页面已准备好，没有检测到需要处理的验证")
                        break

                except Exception as e:
                    self.logger.debug(f"Turnstile检测循环异常: {str(e)}")

                await page.wait_for_timeout(random.uniform(1000, 2000))
                attempts += 1

            return True  # 返回True表示页面已准备好或已处理验证
        except Exception as e:
            self.logger.error(f"Turnstile 验证失败: {str(e)}")
            return False

    async def _get_verification_code(self, api_url: str, email: str) -> Optional[str]:
        """获取邮箱验证码"""
        self.logger.info(f"正在获取邮箱 {email} 的验证码")
        try:
            # 记录当前时间戳，用于过滤邮件
            timestamp = int(time.time()) - 5  # 提前5秒以确保不会错过邮件

            # 等待邮件到达
            await asyncio.sleep(5)  # 等待邮件发送和接收

            max_retries = 10
            for attempt in range(max_retries):
                try:
                    async with aiohttp.ClientSession() as session:
                        params = {
                            'to': email,
                            'from': 'cursor.sh',  # 发件人包含的关键字
                            'timestamp': timestamp,
                            'fuzzy': 'true'  # 模糊匹配
                        }

                        self.logger.info(f"尝试获取验证码，第 {attempt+1}/{max_retries} 次")
                        async with session.get(f"{api_url}/find-emails", params=params) as response:
                            if response.status == 200:
                                emails_data = await response.json()

                                if emails_data and len(emails_data) > 0:
                                    # 查找验证码
                                    for email_data in emails_data:
                                        email_text = email_data.get('text', '')
                                        # 使用正则表达式查找6位数字验证码
                                        code_match = re.search(r'\b(\d{6})\b', email_text)
                                        if code_match:
                                            code = code_match.group(1)
                                            self.logger.info(f"成功获取到验证码: {code}")
                                            return code
                            else:
                                self.logger.error(f"获取邮件失败，状态码: {response.status}")
                                error_text = await response.text()
                                self.logger.error(f"错误详情: {error_text}")
                except Exception as e:
                    self.logger.error(f"获取验证码请求异常: {str(e)}")

                # 等待一段时间后重试
                await asyncio.sleep(3)

            self.logger.error(f"获取验证码失败，已达到最大重试次数 {max_retries}")
            return None
        except Exception as e:
            self.logger.error(f"获取验证码过程出错: {str(e)}")
            return None

    async def _get_cursor_session_token(self, page: Page, max_attempts: int = 5, retry_interval: int = 3) -> Tuple[Optional[str], Optional[str]]:
        """获取Cursor会话Token"""
        try:
            # 导航到设置页面
            await page.goto("https://cursor.sh/settings", timeout=60000)
            await page.wait_for_timeout(5000)

            # 尝试获取使用量信息
            try:
                usage_selector = (
                    "css:div.col-span-2 > div > div > div > div > "
                    "div:nth-child(1) > div.flex.items-center.justify-between.gap-2 > "
                    "span.font-mono.text-sm\\/\\[0\\.875rem\\]"
                )
                usage_ele = await page.query_selector(usage_selector)
                total_usage = "unknown"
                if usage_ele:
                    usage_text = await usage_ele.text_content()
                    if usage_text:
                        total_usage = usage_text.split("/")[-1].strip()
                        self.logger.info(f"使用限制: {total_usage}")
                else:
                    self.logger.warning("未能找到使用量元素")
            except Exception as e:
                self.logger.warning(f"获取使用量信息失败: {str(e)}")
                # 继续执行，不要因为获取使用量失败而中断整个流程

            self.logger.info("获取Cookie中...")
            attempts = 0

            while attempts < max_attempts:
                try:
                    cookies = await page.context.cookies()
                    for cookie in cookies:
                        if cookie.get("name") == "WorkosCursorSessionToken":
                            token_value = cookie["value"]
                            parts = token_value.split("%3A%3A")
                            if len(parts) >= 2:
                                user = parts[0]
                                token = parts[1]
                                self.logger.info(f"获取到账号Token: {token}, 用户: {user}")
                                return token, user

                    attempts += 1
                    if attempts < max_attempts:
                        self.logger.warning(f"未找到Cursor会话Token，重试中... ({attempts}/{max_attempts})")
                        await page.wait_for_timeout(retry_interval * 1000)
                    else:
                        self.logger.info("未找到Cursor会话Token，已达到最大尝试次数")

                except Exception as e:
                    self.logger.error(f"获取Token出错: {str(e)}")
                    attempts += 1
                    if attempts < max_attempts:
                        self.logger.info(f"重试获取Token，等待时间: {retry_interval}秒，尝试次数: {attempts}/{max_attempts}")
                        await page.wait_for_timeout(retry_interval * 1000)

            return None, None

        except Exception as e:
            self.logger.error(f"获取Token过程出错: {str(e)}")
            return None, None

    async def register_single_account(self, user: User, page: Page):
        """注册单个Cursor账号"""
        self.logger.info("=============开始注册账号=============")
        self.logger.info(f"账号信息: 邮箱: {user.email_prefix}{user.email_domain}, 密码: {user.password}, 姓名: {user.first_name} {user.last_name}")

        await page.goto("https://authenticator.cursor.sh", timeout=60000)

        await page.click("a.BrandedLink",timeout=60000,force=True)
        input("Press Enter to continue...")
        # 输入个人信息
        self.logger.info("=============正在填写个人信息=============")
        await page.fill('input[name="first_name"]', user.first_name, timeout=5000)
        self.logger.info(f"已输入名字: {user.first_name}")
        await page.wait_for_timeout(random.uniform(1000, 3000))

        await page.fill('input[name="last_name"]', user.last_name, timeout=5000)
        self.logger.info(f"已输入姓氏: {user.last_name}")
        await page.wait_for_timeout(random.uniform(1000, 3000))

        await page.fill('input[type="email"]', f"{user.email_prefix}{user.email_domain}", timeout=5000)
        self.logger.info(f"已输入邮箱: {user.email_prefix}{user.email_domain}")
        await page.wait_for_timeout(random.uniform(1000, 3000))
        # 提交个人信息
        self.logger.info("=============提交个人信息=============")
        await page.click('button[type="submit"]', timeout=60000)



        input("Press Enter to continue...")
        # 处理Turnstile验证
        await self._handle_turnstile(page)

        # 检查是否有验证失败的提示
        if await page.is_visible("text=verify the user is human. Please try again.") or \
            await page.is_visible("text=Can't verify the user is human. Please try again."):
            raise Exception("检测到turnstile验证失败，正在重试...")

        input("Press Enter to continue...")
        # 设置密码
        if await page.is_visible('input[name="password"]'):
            self.logger.info(f"设置密码：{user.password}")
            await page.fill('input[name="password"]', user.password, timeout=5000)
            await page.wait_for_timeout(random.uniform(1000, 2000))

            self.logger.info("提交密码...")
            await page.click('button[type="submit"]', timeout=5000)
            self.logger.info("密码设置成功,等待系统响应....")

        input("Press Enter to continue...")
        # 处理最终验证
        await self._handle_turnstile(page)

        # 检查是否有错误提示
        if await page.is_visible("text=This email is not available."):
            raise Exception("邮箱已被使用")

        if await page.is_visible("text=Sign up is restricted."):
            raise Exception("注册限制")

        input("Press Enter to continue...")
        # 处理邮箱验证码
        if await page.is_visible('[data-index="0"]', timeout=5000):
            self.logger.info("等待输入验证码...")

            # 获取验证码
            proof_email_config = self.config_manager.proof_email[0]
            proof_api = proof_email_config['api']
            proof_email = f"{user.email_prefix}{user.email_domain}"

            # 获取验证码
            code = await self._get_verification_code(proof_api, proof_email)
            if not code:
                raise Exception("未获取到验证码")

            self.logger.info(f"输入验证码: {code}")
            # 输入验证码
            for i, digit in enumerate(code):
                await page.fill(f'[data-index="{i}"]', digit, timeout=2000)
                await page.wait_for_timeout(random.uniform(300, 600))

            self.logger.info("验证码输入完成")
            await page.wait_for_timeout(random.uniform(3000, 5000))

        input("Press Enter to continue...")
        # 完成最终验证
        await self._handle_turnstile(page)
        await page.wait_for_timeout(random.uniform(3000, 5000))

        input("Press Enter to continue...")
        # 检查是否成功进入账号设置页面
        if await page.is_visible("text=Account Settings", timeout=5000):
            self.logger.info("注册成功，已进入账号设置页面")
            # 获取token
            token, user_id = await self._get_cursor_session_token(page)
            if token:
                self.logger.info(f"获取到账号Token: {token}, 用户: {user_id}")
                return token
            else:
                raise Exception("获取Token失败")

    async def do(self, playwright: Playwright, register_count: int = 5, max_concurrent_tasks: int = 1):
        registered_users = []
        success_count = 0
        proxies = self.config_manager.get('proxies', [])
        self.logger.info(f"Total proxies: {len(proxies)}")
        sem = asyncio.Semaphore(max_concurrent_tasks)

        async def register_task(proxy_info: Optional[Dict], task_id: str):
            nonlocal success_count
            self.logger.info(f"Starting task {task_id}")
            proxy_desc = f"proxy {proxy_info['host']}" if proxy_info else "no proxy"

            try:
                async with sem:
                    users = await self.register_with_proxy(
                        playwright,
                        proxy_info,
                        register_count
                    )
                    registered_users.extend(users)
                    success_count += len(users)
                    self.logger.info(f"Task {task_id}: Registered {len(users)}/{register_count} with {proxy_desc}")
                    return users
            except asyncio.CancelledError:
                self.logger.info(f"Task {task_id} was cancelled")
                raise  # 传播取消信号以确保资源清理
            except Exception as e:
                self.logger.error(f"Task {task_id} failed with {proxy_desc}: {str(e)}")
                return []

        # 创建任务列表
        #tasks_def = [(f"proxy_{i}", p) for i, p in enumerate(proxies, 1)]
        tasks_def = [("no_proxy", None)] + [(f"no_proxy_{i}", None) for i in range(10)]
        total_planned = register_count * len(tasks_def)
        tasks = []

        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            # Windows使用默认信号处理
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)

        try:
            # 创建并跟踪所有任务
            tasks = [loop.create_task(register_task(proxy, tid)) for tid, proxy in tasks_def]
            await asyncio.gather(*tasks, return_exceptions=False)
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)

        self.logger.info(f"Completed: {success_count}/{total_planned}")
        return registered_users


    async def register_with_proxy(self, playwright: Playwright, proxy, register_count: int):
        registered_users = []

        # 创建根目录下的cache文件夹
        cache_dir = os.path.join(os.getcwd(), "brower_cache")
        os.makedirs(cache_dir, exist_ok=True)
        print(f"缓存目录设置在: {cache_dir}")

        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': [
                '--no-sandbox',
                '--mute-audio',
                '--disable-setuid-sandbox',
                '--disable-webgl',
                '--ignore-certificate-errors',
                '--ignore-certificate-errors-spki-list',
                '--ignore-ssl-errors',
                f"--disk-cache-dir={cache_dir}",
                "--disk-cache-size=104857600",
                "--enable-features=NetworkServiceInProcess",  # 启用网络服务
                "--disable-features=NetworkService",  # 禁用独立网络服务进程
            ],

        }

        context_options = {
            'ignore_https_errors': True,
            'user_agent': self.browserUserAgent,
        }

        # Add proxy configuration if proxy is provided
        if proxy and proxy.get('host') and proxy['host'].strip() != "":
            proxy_config = {
                'server': proxy['host'],
            }

            # Add authentication if provided
            if proxy.get('username') and proxy.get('password'):
                proxy_config['username'] = proxy['username']
                proxy_config['password'] = proxy['password']

            context_options['proxy'] = proxy_config
            proxy_info = f" with proxy {proxy['host']}"
        else:
            proxy_info = " without proxy"

        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)

        # 添加路由处理
        await context.route('**/*', handle_route)
        page = await context.new_page()

        try:
            await page.goto('https://myip.ipip.net/', wait_until='load', timeout=60000)
            text_content = await page.text_content('body')
            self.logger.info(f"Current IP Address: {text_content}")
        except Exception as e:
            self.logger.error(f"Failed to open ipip page{proxy_info}: {str(e)}")

        try:
            for i in range(register_count):
                try:
                    self.logger.info(f"Registering account {i+1}/{register_count}{proxy_info}")
                    # Clear cookies
                    await context.clear_cookies()

                    # Generate and register user
                    user = self.user_generator.generate_user()
                    user.email_domain = "@"+self.config_manager.proof_email[0]['domain']
                    token = await self.register_single_account(user, page)
                    await self.save(user, token)
                    registered_users.append(user)

                    self.logger.info(f'Cursor account {i+1} Created Successfully{proxy_info}: {user.email_prefix}{user.email_domain}')

                    # Wait before next registration
                    if i < register_count - 1:
                        wait_time = self.config_manager.get('register_interval', 10)
                        self.logger.info(f'Waiting {wait_time} seconds before registering the next account...')
                        await page.wait_for_timeout(wait_time * 1000)
                except IPBadError as e:
                    break
                except Exception as e:
                    self.logger.error(f"Registration failed for account {i+1}{proxy_info}: {str(e)}")
                    await page.wait_for_timeout(5000)  # Wait a moment before continuing
        finally:
            await page.close()
            await context.close()
            await browser.close()

        return registered_users

    async def save(self, user: User,token:str):
        """保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作"""
        try:
            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(self.account_file, 'a+', encoding='utf-8') as f:
                    user_info = f"{user.email_prefix}{user.email_domain},{user.password},{token}\n"
                    await f.write(user_info)
        except Exception as e:
            self.logger.error(f"Failed to save user {user.email_prefix}{user.email_domain}: {str(e)}")