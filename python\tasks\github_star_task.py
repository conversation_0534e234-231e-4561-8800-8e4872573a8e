import asyncio
import logging
import os
from pathlib import Path
import random
import re
from typing import List, Dict, Set, Optional
import aiofiles
from playwright.async_api import Playwright, Page
from utils.config_manager import ConfigManager
from utils.browser_fingerprint import BrowserFingerprint
from utils.msmail import get_email_content


class GitHubStarTask:
    """
    GitHub项目点星任务
    """

    def __init__(
        self,
        config_manager: ConfigManager,
        account_file: str = "data/github_accounts.csv",
        error_file: str = "data/github_star_error_accounts.txt",
    ) -> None:
        """
        初始化GitHub点星任务

        Args:
            config_manager: 配置管理器
            account_file: GitHub账号文件路径
            error_file: 错误账号记录文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.error_file = Path(error_file)
        self.browser_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
        self.file_lock = asyncio.Lock()
        self.fingerprint_generator = BrowserFingerprint()

    async def read_github_accounts(self) -> List[Dict[str, str]]:
        """
        从CSV文件读取GitHub账号信息

        Returns:
            List[Dict[str, str]]: 账号信息列表，包含email, password, username
        """
        accounts = []
        try:
            if not self.account_file.exists():
                self.logger.error(f"账号文件不存在: {self.account_file}")
                return accounts

            async with aiofiles.open(self.account_file, "r", encoding="utf-8") as f:
                content = await f.read()

            for line_num, line in enumerate(content.strip().split("\n"), 1):
                line = line.strip()
                if not line:
                    continue

                parts = line.split(",")
                if len(parts) >= 3:
                    account = {
                        "email": parts[0].strip(),
                        "password": parts[1].strip(),
                        "username": parts[2].strip(),
                    }
                    accounts.append(account)
                else:
                    self.logger.warning(f"第{line_num}行格式不正确: {line}")

            self.logger.info(f"成功读取{len(accounts)}个GitHub账号")
            return accounts

        except Exception as e:
            self.logger.error(f"读取GitHub账号文件时出错: {str(e)}")
            return accounts

    async def get_error_accounts(self) -> Set[str]:
        """
        读取点星失败的账号列表

        Returns:
            Set[str]: 失败账号的邮箱集合
        """
        try:
            if not self.error_file.exists():
                return set()

            async with aiofiles.open(self.error_file, "r", encoding="utf-8") as f:
                content = await f.read()

            error_emails = set()
            for line in content.strip().split("\n"):
                if line.strip():
                    error_emails.add(line.strip())

            self.logger.info(f"读取到{len(error_emails)}个失败账号")
            return error_emails

        except Exception as e:
            self.logger.error(f"读取错误账号文件时出错: {str(e)}")
            return set()

    async def save_error_account(self, email: str) -> None:
        """
        保存点星失败的账号

        Args:
            email: 失败的邮箱地址
        """
        try:
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            async with self.file_lock:
                async with aiofiles.open(self.error_file, "a", encoding="utf-8") as f:
                    await f.write(f"{email}\n")

            self.logger.info(f"已记录失败账号: {email}")

        except Exception as e:
            self.logger.error(f"保存失败账号时出错: {str(e)}")

    async def login_github(self, page: Page, email: str, password: str) -> bool:
        """
        登录GitHub账号

        Args:
            page: 浏览器页面对象
            email: 邮箱地址
            password: 密码

        Returns:
            bool: 登录是否成功
        """
        try:
            self.logger.info(f"开始登录GitHub账号: {email}")

            # 访问GitHub登录页面
            await page.goto("https://github.com/login", timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 模拟人类行为
            await self.fingerprint_generator.simulate_human_behavior(page)

            # 填写邮箱
            await page.fill('input[name="login"]', email)
            await page.wait_for_timeout(random.randint(500, 1500))

            # 填写密码
            await page.fill('input[name="password"]', password)
            await page.wait_for_timeout(random.randint(500, 1500))

            # 点击登录按钮
            await page.click('input[name="commit"]')

            # 等待登录完成，检查是否跳转到主页或其他页面
            try:
                await page.wait_for_url("https://github.com/**", timeout=30000)

                # 检查是否成功登录（查看页面是否包含用户相关元素）
                await page.wait_for_timeout(3000)

                # 检查当前URL
                current_url = page.url

                # 检查是否还在登录页面（登录失败的情况）
                if "login" in current_url:
                    self.logger.error(f"登录失败，仍在登录页面: {email}")
                    return False

                # 检查是否需要设备验证
                if "sessions/verified-device" in current_url:
                    self.logger.info(f"需要进行设备验证: {email}")

                    # 处理设备验证
                    verification_success = await self.handle_device_verification(page, email)
                    if not verification_success:
                        self.logger.error(f"设备验证失败: {email}")
                        return False

                    # 验证完成后再次检查登录状态
                    await page.wait_for_timeout(3000)
                    current_url = page.url
                    if "login" in current_url or "sessions/verified-device" in current_url:
                        self.logger.error(f"设备验证后仍未成功登录: {email}")
                        return False

                self.logger.info(f"GitHub账号登录成功: {email}")
                return True

            except Exception as e:
                self.logger.error(f"登录超时或失败: {email}, 错误: {str(e)}")
                return False

        except Exception as e:
            self.logger.error(f"登录过程中出错: {email}, 错误: {str(e)}")
            return False

    async def handle_device_verification(self, page: Page, email: str) -> bool:
        """
        处理GitHub设备验证

        Args:
            page: 浏览器页面对象
            email: 邮箱地址

        Returns:
            bool: 验证是否成功
        """
        try:
            self.logger.info(f"开始处理设备验证: {email}")

            # 等待验证页面加载完成
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(2000)

            # 等待接收验证邮件
            self.logger.info(f"等待接收设备验证邮件: {email}")
            max_retries = 30
            email_content = ""

            for i in range(max_retries):
                try:
                    email_content = await get_email_content(email)
                    if "Verification code:" in email_content:
                        break
                except Exception as e:
                    self.logger.warning(f"第{i+1}次获取邮件失败: {str(e)}")

                if i < max_retries - 1:
                    self.logger.info(f"等待5秒后重试...")
                    await page.wait_for_timeout(5000)

            if not email_content:
                self.logger.error(f"未能获取到设备验证邮件: {email}")
                return False

            # 提取6位验证码
            verification_code_match = re.search(r'(\d{6})', email_content)
            if not verification_code_match:
                self.logger.error(f"无法从邮件中提取6位验证码: {email}")
                return False

            verification_code = verification_code_match.group(1)
            self.logger.info(f"提取到设备验证码: {verification_code}")

            # 填写验证码
            otp_input_selector = 'input[name="otp"]'
            try:
                await page.wait_for_selector(otp_input_selector, state="visible", timeout=10000)
                await page.fill(otp_input_selector, verification_code)
                await page.wait_for_timeout(1000)

                self.logger.info(f"已填写验证码: {verification_code}")
            except Exception as e:
                self.logger.error(f"填写验证码失败: {str(e)}")
                return False
            
            await page.wait_for_timeout(5000)
            return True
            # 点击验证按钮 填入验证码后会自动点击.下面就不用了
            # verify_button_selectors = [
            #     'button[type="submit"]:has-text("Verify")',
            #     'button.btn-primary:has-text("Verify")',
            #     'button[data-disable-with="Verifying…"]',
            #     'button.btn.btn-block:has-text("Verify")'
            # ]

            # verify_button = None
            # for selector in verify_button_selectors:
            #     try:
            #         verify_button = page.locator(selector).first
            #         if await verify_button.is_visible():
            #             break
            #     except:
            #         continue

            # if not verify_button:
            #     self.logger.error(f"未找到验证按钮: {email}")
            #     return False

            # try:
            #     await verify_button.click()
            #     self.logger.info(f"已点击验证按钮: {email}")

            #     # 等待验证完成
            #     await page.wait_for_timeout(5000)

            #     # 检查是否验证成功（页面跳转）
            #     current_url = page.url
            #     if "sessions/verified-device" not in current_url:
            #         self.logger.info(f"设备验证成功: {email}")
            #         return True
            #     else:
            #         self.logger.warning(f"设备验证可能失败，仍在验证页面: {email}")
            #         return False

            # except Exception as e:
            #     self.logger.error(f"点击验证按钮失败: {str(e)}")
            #     return False

        except Exception as e:
            self.logger.error(f"设备验证过程中出错: {email}, 错误: {str(e)}")
            return False

    async def star_repository(self, page: Page, repo_url: str) -> bool:
        """
        为指定的GitHub仓库点星

        Args:
            page: 浏览器页面对象
            repo_url: 仓库URL

        Returns:
            bool: 点星是否成功
        """
        try:
            self.logger.info(f"开始为仓库点星: {repo_url}")

            # 访问仓库页面
            await page.goto(repo_url, timeout=60000)
            await page.wait_for_load_state("networkidle")

            # 等待页面加载完成
            await page.wait_for_timeout(2000)

            toggler_container = await page.query_selector('.js-toggler-container')
            # 检查是否已经点过星
            try:
                class_list = await toggler_container.get_attribute('class')
                if class_list and 'on' in class_list.split():
                    self.logger.info(f"仓库已经被点星: {repo_url}")
                    return True
            except:
                pass

            # 模拟人类行为
            #await self.fingerprint_generator.simulate_human_behavior(page)

            # 查找star按钮 - 使用更精确的选择器
            star_button_selectors = [
                '.js-toggler-container .unstarred button',
            ]

            star_button = None
            for selector in star_button_selectors:
                try:
                    star_button = page.locator(selector).first
                    if await star_button.is_visible():
                        break
                except:
                    continue

            if not star_button:
                self.logger.error(f"未找到star按钮: {repo_url}")
                return False
            else:
                button_text = await star_button.text_content()
                self.logger.info(f"找到star按钮: {selector}, 按钮文本: {button_text.replace("\n", "")}")

            # 点击star按钮
            await star_button.click()
            await page.wait_for_timeout(10000)

            # 验证点星是否成功
            try:
                button_text = await star_button.text_content()
                class_list = await toggler_container.get_attribute('class')
                if class_list and 'on' in class_list.split():
                    self.logger.info(f"仓库点星成功: {repo_url}, 按钮文本: {button_text.replace("\n", "")}")
                    return True
                else:
                    self.logger.warning(f"点星状态不明确: {repo_url}, 按钮文本: {button_text.replace("\n", "")}")
                    return True  # 假设成功，因为有些情况下按钮文本可能不会立即更新
            except Exception as e:
                self.logger.warning(f"验证点星状态时出错: {str(e)}, 假设成功")
                return True

        except Exception as e:
            self.logger.error(f"点星过程中出错: {repo_url}, 错误: {str(e)}")
            return False

    async def star_single_account(
        self, account: Dict[str, str], repo_url: str, playwright: Playwright
    ) -> bool:
        """
        使用单个账号为仓库点星

        Args:
            account: 账号信息字典
            repo_url: 仓库URL
            playwright: Playwright实例

        Returns:
            bool: 操作是否成功
        """
        email = account["email"]
        password = account["password"]

        try:
            # 为每个账号生成新的浏览器指纹
            fingerprint = self.fingerprint_generator.generate_fingerprint()
            self.logger.info(f"为账号 {email} 生成新的浏览器指纹")

            # 获取浏览器选项和上下文选项
            browser_options = self.fingerprint_generator.get_browser_options(fingerprint)
            context_options = self.fingerprint_generator.get_context_options(fingerprint)

            # 启动浏览器并创建上下文
            browser = await playwright.chromium.launch(**browser_options)
            context = await browser.new_context(**context_options)

            # 注入反检测脚本
            await self.fingerprint_generator.inject_anti_detection_script(context, fingerprint)

            page = await context.new_page()

            try:
                # 登录GitHub
                login_success = await self.login_github(page, email, password)
                if not login_success:
                    await self.save_error_account(email)
                    return False

                # 为仓库点星
                star_success = await self.star_repository(page, repo_url)
                if not star_success:
                    await self.save_error_account(email)
                    return False

                self.logger.info(f"账号 {email} 成功为仓库点星: {repo_url}")
                return True

            finally:
                # 关闭浏览器资源
                try:
                    await page.close()
                    await context.close()
                    await browser.close()
                except Exception as e:
                    self.logger.error(f"关闭浏览器时出错: {str(e)}")

        except Exception as e:
            self.logger.error(f"账号 {email} 点星失败: {str(e)}")
            await self.save_error_account(email)
            return False

    async def do(
        self,
        playwright: Playwright,
        repo_url: str,
        max_concurrent_tasks: int = 3,
        account_limit: Optional[int] = None,
    ) -> Dict[str, int]:
        """
        执行GitHub点星任务

        Args:
            playwright: Playwright实例
            repo_url: 要点星的仓库URL
            max_concurrent_tasks: 最大并发任务数
            account_limit: 使用的账号数量限制，None表示使用所有账号

        Returns:
            Dict[str, int]: 包含成功和失败数量的统计信息
        """
        self.logger.info(f"开始执行GitHub点星任务: {repo_url}")

        # 读取GitHub账号
        all_accounts = await self.read_github_accounts()
        if not all_accounts:
            self.logger.error("没有可用的GitHub账号")
            return {"success": 0, "failed": 0, "total": 0}

        # 读取失败账号列表
        error_accounts = await self.get_error_accounts()

        # 过滤掉失败的账号
        available_accounts = [
            account for account in all_accounts
            if account["email"] not in error_accounts
        ]

        if not available_accounts:
            self.logger.error("没有可用的GitHub账号（所有账号都在失败列表中）")
            return {"success": 0, "failed": 0, "total": 0}

        # 限制账号数量
        if account_limit and account_limit > 0:
            available_accounts = available_accounts[:account_limit]

        self.logger.info(f"将使用 {len(available_accounts)} 个账号进行点星")

        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent_tasks)

        async def star_task(account: Dict[str, str]) -> bool:
            async with semaphore:
                # 添加随机延迟避免被检测
                delay = random.randint(1, 5)
                await asyncio.sleep(delay)

                return await self.star_single_account(account, repo_url, playwright)

        # 执行并发任务
        self.logger.info(f"开始并发执行点星任务，最大并发数: {max_concurrent_tasks}")
        tasks = [star_task(account) for account in available_accounts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = 0
        failed_count = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"任务 {i+1} 执行异常: {str(result)}")
                failed_count += 1
            elif result:
                success_count += 1
            else:
                failed_count += 1

        total_count = len(available_accounts)

        self.logger.info(f"GitHub点星任务完成: 成功 {success_count}/{total_count}, 失败 {failed_count}")

        return {
            "success": success_count,
            "failed": failed_count,
            "total": total_count
        }