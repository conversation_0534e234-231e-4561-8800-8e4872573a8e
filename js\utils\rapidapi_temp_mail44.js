const tempMailNewEndpoint = 'temp-mail44.p.rapidapi.com'; // 临时邮箱 API 域名
const rapidApiKey = '**************************************************'; // RapidAPI 密钥

// 需要过滤的邮箱域名列表
const blockedDomains = [
    'qacmjeq.com',
    'bltiwd.com',
    'knmcadibav.com',
    'cmhvzylmfc.com',
    'daouse.com',
    'qzueos.com',
    "wywnxa.com",
    "zudpck.com",
    "jkotypc.com",
    "vwhins.com",
    "illubd.com",
    "wyoxafp.com",
    "osxofulk.com",
    "zvvzuv.com"
];

export const generateTempEmail = async (retryCount = 0) => {
    // 设置最大重试次数，防止无限递归
    const maxRetries = 5;
    if (retryCount >= maxRetries) {
        console.error(`已达到最大重试次数 ${maxRetries}，无法生成有效的临时邮箱`);
        throw new Error(`已达到最大重试次数 ${maxRetries}，无法生成有效的临时邮箱`);
    }

    const options = {
        method: 'POST',
        headers: {
            'x-rapidapi-key': rapidApiKey,
            'x-rapidapi-host': tempMailNewEndpoint,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ key1: 'value', key2: 'value' })
    };

    try {
        const response = await fetch(`https://${tempMailNewEndpoint}/api/v3/email/new`, options);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        //{"email":"<EMAIL>","token":"2MvO76rdKqOKHyF2yiLM"}
        const result = await response.json();

        // 检查生成的邮箱是否包含被屏蔽的域名
        if (result && result.email) {
            const emailDomain = result.email.split('@')[1];
            if (blockedDomains.includes(emailDomain)) {
                console.log(`邮箱域名 ${emailDomain} 在屏蔽列表中，重新生成...`);
                // 递归调用自身，重新生成邮箱
                return generateTempEmail(retryCount + 1);
            }
        }

        return result;
    } catch (error) {
        console.error("生成临时邮箱失败:", error);
        throw error;
    }
};

export const getEmailContent = async (email) => {
    const options = {
        method: 'GET',
        headers: {
            'x-rapidapi-key': rapidApiKey,
            'x-rapidapi-host': tempMailNewEndpoint
        }
    };

    try {
        const response = await fetch(`https://${tempMailNewEndpoint}/api/v3/email/${email}/messages`, options);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const emailContent = await response.json();
        if (emailContent && emailContent.length > 0) {
            const bodyText = emailContent[0].body_text;
            return bodyText; 
        }
        return null;
    } catch (error) {
        console.error("获取邮件内容失败:", error);
        throw error;
    }
};
