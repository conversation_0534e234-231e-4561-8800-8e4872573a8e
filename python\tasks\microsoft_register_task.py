import json
import logging
import os
from pathlib import Path
import signal
import sys
import time
from typing import Union, List
import aiofiles
import requests
from twocaptcha import TwoCaptcha
from utils.cache_helper import handle_route, setup_page_caching
from utils.config_manager import ConfigManager
from utils.user_generator import User, UserGenerator
from playwright.async_api import async_playwright,Playwright,Page, Frame,Request,Route
from urllib.parse import urlparse, parse_qs
import asyncio
from typing import List, Dict, Optional
from utils.captcha_helper import CaptchaHelper

# 定义自定义异常类
class IPBadError(Exception):
    """自定义的异常类"""
    def __init__(self, message="IPBadError"):
        self.message = message
        super().__init__(self.message)
    def __str__(self):
        return f"IPBadError: {self.message}"

class MicrosoftRegisterTask:
    """
    邮箱注册任务
    """
    def __init__(
        self,
        config_manager: Config<PERSON>anager,
        account_file: Union[str, Path] = "microsoft_accounts.csv"
    ) -> None:
        """
        初始化配置管理器
        
        Args:
            config_manager: 配置管理
            account_file: 账号文件
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.user_generator = UserGenerator(config_manager)
        self.two_captcha = TwoCaptcha(self.config_manager.two_captcha_key, defaultTimeout=600)
        self.browserUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.file_lock = asyncio.Lock()
        self.captcha_helper = CaptchaHelper(
            two_captcha_key=self.config_manager.two_captcha_key,
            captcharun_token=self.config_manager.captcharun_token,
            browser_user_agent=self.browserUserAgent
        )


    async def register_single_account(self,user:User, page: Page):
        """注册单个账号""" 
        await page.goto("https://outlook.live.com/owa/?nlp=1&signup=1", timeout=60000)

        # 等待页面加载完成
        await page.wait_for_selector('#pageContent')
        await page.click('#nextButton', timeout=60000)
 
        if user.email_domain == "@hotmail.com":
            await page.select_option('#domainSelect', 'hotmail.com')
        
        # 输入邮箱
        self.logger.info(f"Email: {user.email_prefix}{user.email_domain}")
        await page.fill('#usernameInput', user.email_prefix, timeout=5000)
        await page.click('#nextButton', timeout=60000)
        
        # 输入密码
        self.logger.info(f"Password: {user.password}")
        await page.fill('#Password', user.password, timeout=5000)
        await page.fill('#Password', user.password, timeout=5000,force=True) # 重复填写一次，防止填写失败
        await page.click('#nextButton', timeout=60000)
        
        # 输入姓名
        self.logger.info(f"Name: {user.first_name} {user.last_name}")
        await page.fill('#firstNameInput', user.first_name, timeout=5000)
        await page.fill('#lastNameInput', user.last_name, timeout=5000)
        await page.fill('#firstNameInput', user.first_name, timeout=5000,force=True)# 重复填写一次，防止填写失败
        await page.click('#nextButton', timeout=60000)
        
        # 选择国家和生日
        self.logger.info(f"country_region: {user.country_region}, birthday: {user.birthday}")
        birth_arr = user.birthday.split('-')
        await page.select_option('#countryRegionDropdown', user.country_region)
        await page.fill('#BirthYear', birth_arr[0])
        await page.select_option('#BirthMonth', birth_arr[1].lstrip('0'))
        await page.select_option('#BirthDay', birth_arr[2].lstrip('0'))
        await page.click('#nextButton', timeout=60000)
        
        # 处理验证码
        self.logger.info(f"Waiting for captcha...") 
        
        riskApiBlocked = False
        try:
            await page.wait_for_selector("#riskApiBlockedViewTitle",timeout=3000)
            riskApiBlocked = True
        except Exception as e:
            self.logger.info(f"ip可以被正常注册: {str(e)}")
        
        if riskApiBlocked:
            raise IPBadError("IP被封禁")
        
        # 获得sitekey
        enforcement_frame = page.frame_locator('#enforcementFrame')
        verification_challenge = enforcement_frame.frame_locator('iframe')
        fc_token_input = verification_challenge.locator('#FunCaptcha-Token').first
        fc_token = await fc_token_input.input_value()
        self.logger.info(f"fc_token:{fc_token}")
        params = dict(param.split('=', 1) for param in fc_token.split('|') if '=' in param)
        sitekey =  params.get('pk', '')
        surl = params.get('surl', '').replace('%2F', '/').replace('%3A', ':')
        self.logger.info(f"url:{page.url},sitekey:{sitekey}.surl:{surl}")
        # 获得url中data值
        enforcementFrameSrc = await (page.locator('#enforcementFrame').get_attribute('src'))
        parsed_url = urlparse(enforcementFrameSrc)
        query_params = parse_qs(parsed_url.query)
        data_value = query_params.get('data', [''])[0]
        code = self.captcha_helper.get_captcharun_token(site_key=sitekey, blob=data_value,surl=surl,siteReferer="https://signup.live.com/signup")
        self.logger.info(f"token:{str(code)}")
        # 构造JavaScript代码字符串,注入脚本
        script = '''
        (token) => {
            console.log('token:',token)
            // 这里的代码在 iframe 的上下文中运行
            let script = document.createElement('SCRIPT');
            script.append('function captchaSubmit(token) { parent.postMessage(JSON.stringify({ eventId: "challenge-complete", payload: { sessionToken: token } }), "*") }');
            document.documentElement.appendChild(script);
            captchaSubmit(token); // 直接调用函数
        }
        '''
        frame: Frame = page.frame(url=enforcementFrameSrc) 
        await frame.evaluate(script, code)
                
        # 确认登录
        try:
            await page.wait_for_url('https://login.live.com/ppsecure/**', timeout=120000)
            await page.click('button[type="submit"]')
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")

     
    async def do(self, playwright: Playwright, register_count: int = 5, max_concurrent_tasks: int = 1):
        registered_users = []
        success_count = 0
        proxies = self.config_manager.get('proxies', [])
        self.logger.info(f"Total proxies: {len(proxies)}")
        sem = asyncio.Semaphore(max_concurrent_tasks)

        async def register_task(proxy_info: Optional[Dict], task_id: str):
            nonlocal success_count
            self.logger.info(f"Starting task {task_id}")
            proxy_desc = f"proxy {proxy_info['host']}" if proxy_info else "no proxy"
            
            try:
                async with sem:
                    users = await self.register_with_proxy(
                        playwright,
                        proxy_info,
                        register_count
                    )
                    registered_users.extend(users)
                    success_count += len(users)
                    self.logger.info(f"Task {task_id}: Registered {len(users)}/{register_count} with {proxy_desc}")
                    return users
            except asyncio.CancelledError:
                self.logger.info(f"Task {task_id} was cancelled")
                raise  # 传播取消信号以确保资源清理
            except Exception as e:
                self.logger.error(f"Task {task_id} failed with {proxy_desc}: {str(e)}")
                return []

        # 创建任务列表
        #tasks_def = [("no_proxy", None)] + [(f"proxy_{i}", p) for i, p in enumerate(proxies, 1)]
        tasks_def = [(f"proxy_{i}", p) for i, p in enumerate(proxies, 1)]
        #tasks_def = [("no_proxy", None)] + [(f"no_proxy_{i}", None) for i in range(10)]
        total_planned = register_count * len(tasks_def)
        tasks = []

        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == 'win32':
            # Windows使用默认信号处理
            signal.signal(signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint))
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)

        try:
            # 创建并跟踪所有任务
            tasks = [loop.create_task(register_task(proxy, tid)) for tid, proxy in tasks_def]
            await asyncio.gather(*tasks, return_exceptions=False)
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != 'win32':
                loop.remove_signal_handler(signal.SIGINT)

        self.logger.info(f"Completed: {success_count}/{total_planned}")
        return registered_users


   


    async def register_with_proxy(self, playwright: Playwright, proxy, register_count: int):
        registered_users = []
        
        # 创建根目录下的cache文件夹
        cache_dir = os.path.join(os.getcwd(), "brower_cache")
        os.makedirs(cache_dir, exist_ok=True)
        print(f"缓存目录设置在: {cache_dir}")

        # Configure proxy settings if provided
        browser_options = {
            'headless': False,
            'args': [
                '--no-sandbox',
                '--mute-audio',
                '--disable-setuid-sandbox',
                '--disable-webgl',
                '--ignore-certificate-errors',
                '--ignore-certificate-errors-spki-list',
                '--ignore-ssl-errors',
                f"--disk-cache-dir={cache_dir}",
                "--disk-cache-size=104857600",
                "--enable-features=NetworkServiceInProcess",  # 启用网络服务
                "--disable-features=NetworkService",  # 禁用独立网络服务进程
            ],
     
        }
        
        context_options = {
            'ignore_https_errors': True,
            'user_agent': self.browserUserAgent,
        }
        
        # Add proxy configuration if proxy is provided
        if proxy and proxy.get('host') and proxy['host'].strip() != "":
            proxy_config = {
                'server': proxy['host'],
            }
            
            # Add authentication if provided
            if proxy.get('username') and proxy.get('password'):
                proxy_config['username'] = proxy['username']
                proxy_config['password'] = proxy['password']
            
            context_options['proxy'] = proxy_config
            proxy_info = f" with proxy {proxy['host']}"
        else:
            proxy_info = " without proxy"
        
        # Launch browser and create context
        browser = await playwright.chromium.launch(**browser_options)
        #browser = await playwright.chromium.connect_over_cdp('wss://browser.zenrows.com?apikey=****************************************&proxy_country=cn')
        context = await browser.new_context(**context_options)

        # 添加路由处理
        await context.route('**/*', handle_route)
        # await context.route(
        # '**/*.{js,css,mjs,jsx,ts,tsx,jpg,jpeg,png,gif,svg,webp,avif,ico,bmp,tiff,woff,woff2,ttf,otf,eot,mp3,mp4,webm,ogg,wav,pdf,json,xml,map}',
        # setup_page_caching
        # )
        page = await context.new_page()

        try:
            await page.goto('https://myip.ipip.net/',wait_until='load',timeout=60000)
            text_content = await page.text_content('body')
            self.logger.info(f"Current IP Address: {text_content}")
        except Exception as e:
            self.logger.error(f"Failed to open ipip page{proxy_info}: {str(e)}")

        try:
            for i in range(register_count):
                try:
                    self.logger.info(f"Registering account {i+1}/{register_count}{proxy_info}")
                    # Clear cookies
                    await context.clear_cookies()
                    
                    # Generate and register user
                    user = self.user_generator.generate_user()
                    await self.register_single_account(user, page)
                    await self.save(user,proxy_info)
                    registered_users.append(user)
                    
                    self.logger.info(f'Email {i+1} Created Successfully{proxy_info}: {user.email_prefix}{user.email_domain}')
                    
                    # Wait before next registration
                    if i < register_count - 1:
                        wait_time = self.config_manager.get('register_interval', 10)
                        self.logger.info(f'Waiting {wait_time} seconds before registering the next account...')
                        await page.wait_for_timeout(wait_time * 1000)
                except IPBadError as e:
                    break
                except Exception as e:
                    self.logger.error(f"Registration failed for account {i+1}{proxy_info}: {str(e)}")
                    await page.wait_for_timeout(5000)  # Wait a moment before continuing
        finally:
            await page.close()
            await context.close()
            await browser.close()
      
        return registered_users
    
    async def save(self, user: User,proxy:str = None):
        """保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作"""
        try:
            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(self.account_file, 'a+', encoding='utf-8') as f:
                    user_info = f"{user.email_prefix}{user.email_domain},{user.password},{user.first_name} {user.last_name},{user.birthday},{proxy}\n"
                    await f.write(user_info)
        except Exception as e:
            self.logger.error(f"Failed to save user {user.email_prefix}{user.email_domain}: {str(e)}")