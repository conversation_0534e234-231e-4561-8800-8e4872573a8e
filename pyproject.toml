[project]
name = "microsoft-mail"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "2captcha-python>=1.5.1",
    "aiofiles>=24.1.0",
    "aiohttp>=3.11.11",
    "beautifulsoup4>=4.13.4",
    "brotli>=1.1.0",
    "camoufox[geoip]>=0.4.11",
    "playwright>=1.52.0",
    "pyotp>=2.9.0",
    "pyperclip>=1.9.0",
    "python-dotenv>=1.0.1",
    "unique-names-generator>=1.0.2",
]



[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"

[dependency-groups]
dev = [
    "poethepoet>=0.32.1",
]


[tool.poe.tasks]
dev = "uv run python/main.py"
freeze =  { shell = "uv pip compile pyproject.toml -o requirements.txt" }
