import json
import random
import string
import re
import uuid
import aiohttp
import asyncio
import aiofiles
import pyotp
import logging
import os
from bs4 import BeautifulSoup
from datetime import datetime
from pathlib import Path
from typing import Union, List, Dict, Set
from camoufox.async_api import AsyncCamoufox
from utils.msmail import accounts, get_email_content
from utils.browser_fingerprint import BrowserFingerprint


class TargonRegisterTask:
    def __init__(
        self,
        account_file: Union[str, Path] = "data/targon_accounts.csv",
        error_file: Union[str, Path] = "data/targon_error_accounts.txt",
    ):
        self.logger = logging.getLogger(__name__)
        self.account_file = Path(account_file)
        self.error_file = Path(error_file)
        self.email_address = None
        self.manual_code = None  # 存储2FA手动码
        self.file_lock = asyncio.Lock()
        self.fingerprint_generator = BrowserFingerprint()

    async def get_registered_emails(self) -> Set[str]:
        """
        读取已经注册的账号，避免重复注册

        Returns:
            Set[str]: 已注册的邮箱集合
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 检查CSV文件是否存在
            if not self.account_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.account_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return set()

            # 读取并解析CSV文件
            async with aiofiles.open(self.account_file, "r", encoding="utf-8") as f:
                content = await f.read()

            # 提取邮箱（CSV的第一列）
            emails = set()
            for line in content.strip().split("\n"):
                if line.strip():
                    email = line.split(",")[0]
                    emails.add(email.strip())

            self.logger.info(f"已从{self.account_file}读取{len(emails)}个已注册账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取已注册账号时出错: {str(e)}")
            return set()

    async def get_error_emails(self) -> List[str]:
        """
        读取注册失败的错误账号，避免重复注册

        Returns:
            List[str]: 注册失败的邮箱列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            # 检查错误账号文件是否存在
            if not self.error_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.error_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return []

            # 读取并解析文件
            async with aiofiles.open(self.error_file, "r", encoding="utf-8") as f:
                content = await f.read()

            emails = [
                line.strip() for line in content.strip().split("\n") if line.strip()
            ]

            self.logger.info(f"已从{self.error_file}读取{len(emails)}个注册失败的账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取注册失败账号时出错: {str(e)}")
            return []

    async def save_error_account(self, email: str) -> None:
        """
        保存注册失败的账号到文件

        Args:
            email: 注册失败的邮箱
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            async with self.file_lock:
                async with aiofiles.open(self.error_file, "a", encoding="utf-8") as f:
                    await f.write(f"{email}\n")
            self.logger.info(f"成功保存注册失败账号 {email} 到 {self.error_file}")
        except Exception as e:
            self.logger.error(f"保存注册失败账号时出错: {str(e)}")

    def generate_password(self):
        """生成随机密码"""
        return "Dean0104@targon"
        length = 12
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return "".join(random.choice(chars) for _ in range(length))

    async def register_account_http(
        self, session: aiohttp.ClientSession, password: str
    ):
        """注册Targon账户"""
        try:
            self.logger.info("开始注册账户...")

            register_data = {
                "0": {
                    "json": {
                        "email": self.email_address,
                        "password": password,
                        "turnstileToken": "0.sBkmqtFNZ9bndCpInlLMKv-gYBeOoLY6mP376iFur7x9ao9i1pmJORthtKXWMEk_tZWxwuga8M0m_e8WOVKfZNczI10sH91DstqrwPXQNoZ_aI824QmyZcSh21KqarimoW3ufLGuHCk71ebAj-EM_oYNByr6Z2zHs3L120zV3VgPljB4a310misuAktFXnclSVNv7cHHWBOuEKiI3uWrcWzJGxN2ej6p6R33oyAuCR1wAuOyJpIsF0A_Xw_mMakAK3mf7D0-6YMGl1QvKhmHYYmuz9Rl2Q9gS0kpn9n8XMNAum_ADRzzYemYKG-N6O8aFBYc8I7T7sfTOd1vKzravlP7mdAnOU6J_4bF1IhO1DI7NNfQJ5xUJYsZHgDAWraksViZdMfUUrQM73P7sVY9dwBnl1cDOp4uqYfPqD8M4MFjuX06Lg2YSK1CoZAWuFO4z-mlT70L644eHsMFhhvpEtvAoBxq3QKIgL9y6PjdvKt2amyloGncQPnZ5gOKC7uR660PjdC5EBoTPMUvKJB8sF8JbheNuRKJWolmthP_KnbHGmnSbbVe_1F1VAWBHFqrjxThSMjGfEcNinbAGaolEqAbzJYstUQFgjzERAHiDkQYHLR9GKI7E2V7mRJH-zZ8elpDOQ2Zq5IGh-uQDIWOOFAuek8PQN7yNh9rYP0x5azEdrAPiBv7QPu6cEXGCzVLI546_tb_8EaqiFrxceaEAN6vO_PtpIwuoDnpG305V9WpJ15FQtofe0Fshe1GSdzu0FHKwdaNW1gatncRoWNbXrUKLeVkV3rqGAYKss7buCfkQsECu2SJVoKrtjJDgfmG5EMD2Rhyry6KDlIZBfSHIDtUHIun-ySAQ2xqMhCOanJquc6pPkM0sTh1b2UtyS3_Kn-4y6luqqyCoza814HUdjxzFRnDH8Bnq6gP4sL2Kbs.yN5RtLNGBa5SdyJmnPZ80w.5e5cfb4ce32a5758d54019d1aa077ed055e69d3789805e684bec75d0d7cc7639",
                    }
                }
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Accept": "*/*",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sec-gpc": "1",
                "visitor-id": str(uuid.uuid4()),
            }
            headers.update(
                {"Content-Type": "application/json", "x-trpc-source": "react"}
            )

            async with session.post(
                "https://targon.com/api/trpc/account.createAccount?batch=1",
                json=register_data,
                headers=headers,
            ) as response:
                if response.status == 200:
                    self.logger.info("账户注册成功")
                    return True
                else:
                    response_text = await response.text()
                    self.logger.error(
                        f"注册失败: {response.status}, 响应内容: {response_text}"
                    )
                    return False

        except Exception as e:
            self.logger.error(f"注册异常: {e}")
            return False

    async def register_account(self, page, password: str):
        """使用Playwright注册Targon账户"""
        try:
            self.logger.info("开始注册账户...")

            # 访问注册页面
            await page.goto("https://targon.com/sign-in?mode=signup", timeout=60000)

            self.logger.info("已访问注册页面...")
            # 模拟人类行为
            # await self.fingerprint_generator.simulate_human_behavior(page)

            # 填写邮箱
            await page.fill("#email", self.email_address)
            self.logger.info(f"已填写邮箱: {self.email_address}")
            await page.wait_for_timeout(random.randint(500, 1000))

            # 点击继续按钮
            await page.click('button[type="submit"]')
            self.logger.info("已点击继续按钮")

            # 等待500ms
            await page.wait_for_timeout(2000)

            # 填写密码
            await page.fill("#password", password)
            self.logger.info("已填写密码")
            await page.wait_for_timeout(random.randint(300, 800))

            # 填写确认密码
            await page.fill("#password2", password)
            self.logger.info("已填写确认密码")
            await page.wait_for_timeout(random.randint(300, 800))

            # 点击注册按钮
            await page.click('button[type="submit"]')
            self.logger.info("已点击注册按钮")

            # 等待注册完成
            await page.wait_for_timeout(3000)
            self.logger.info("账户注册成功")
            return True

        except Exception as e:
            self.logger.error(f"注册异常: {e}")
            return False

    async def get_activation_link(self, max_attempts=3, delay=5):
        """获取激活链接"""
        try:
            self.logger.info("等待激活邮件...")

            for attempt in range(max_attempts):
                self.logger.info(f"检查邮件 (第 {attempt + 1}/{max_attempts} 次)...")

                try:
                    email_content = await get_email_content(self.email_address, True)
                    if "Targon" in email_content:
                        self.logger.info("找到Targon验证邮件!")

                        # 从HTML内容中提取激活链接
                        soup = BeautifulSoup(email_content, "html.parser")
                        links = soup.find_all("a", href=True)

                        for link in links:
                            href = link["href"]
                            if "email-verification" in href and "token=" in href:
                                self.logger.info("成功提取激活链接")
                                return href

                except Exception as e:
                    self.logger.warning(f"第{attempt+1}次获取邮件失败: {str(e)}")

                if attempt < max_attempts - 1:
                    self.logger.info(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)

            self.logger.error("未能获取到激活链接")
            return None

        except Exception as e:
            self.logger.error(f"获取激活链接异常: {e}")
            return None

    async def activate_email(
        self, session: aiohttp.ClientSession, activation_link: str
    ):
        """激活邮箱"""
        try:
            self.logger.info("开始激活邮箱...")

            # 设置激活请求头
            activation_headers = {
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "none",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            }

            # 手动处理重定向
            current_url = activation_link
            max_redirects = 5
            redirect_count = 0

            while redirect_count < max_redirects:
                async with session.get(
                    current_url, headers=activation_headers, allow_redirects=False
                ) as response:

                    # 检查Set-Cookie头
                    set_cookie_headers = response.headers.getall("set-cookie", [])
                    for set_cookie in set_cookie_headers:
                        if "auth_session=" in set_cookie:
                            # 提取auth_session的值
                            parts = set_cookie.split(";")
                            for part in parts:
                                if part.strip().startswith("auth_session="):
                                    self.session_cookie = part.strip().split("=", 1)[1]
                                    self.logger.info("获取到登录凭证")
                                    break

                    # 处理重定向
                    if response.status in [301, 302, 307, 308]:
                        location = response.headers.get("Location")
                        if not location:
                            break

                        # 处理相对URL
                        if location.startswith("/"):
                            from urllib.parse import urljoin

                            current_url = urljoin("https://targon.com", location)
                        else:
                            current_url = location

                        redirect_count += 1
                        continue

                    elif response.status == 200:
                        self.logger.info("邮箱激活成功")
                        return True
                    else:
                        self.logger.error(f"激活失败: {response.status}")
                        return False

            self.logger.error("重定向次数过多")
            return False

        except Exception as e:
            self.logger.error(f"激活异常: {e}")
            return False

    async def setup_2fa(self, session: aiohttp.ClientSession):
        """设置2FA"""
        try:
            if not self.session_cookie:
                self.logger.error("没有有效的登录凭证")
                return False

            self.logger.info("开始设置2FA...")

            # 创建2FA
            headers = {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Accept": "*/*",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sec-gpc": "1",
                "visitor-id": str(uuid.uuid4()),
                "Content-Type": "application/json",
                "x-trpc-source": "react",
                "Referer": "https://targon.com/two-factor-auth",
            }

            cookies = {"auth_session": self.session_cookie}

            # 获取2FA密钥
            async with session.get(
                "https://targon.com/api/trpc/account.createTwoFactorURI?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D",
                headers=headers,
                cookies=cookies,
            ) as response:

                if response.status != 200:
                    self.logger.error(f"创建2FA失败: {response.status}")
                    return False

                response_data = await response.json()
                result_data = response_data[0]["result"]["data"]["json"]
                two_factor_secret = result_data["twoFactorSecret"]
                manual_code = result_data["manualCode"]

                # 存储manual_code到实例变量
                self.manual_code = manual_code

                # 生成TOTP验证码
                totp = pyotp.TOTP(manual_code)
                otp_code = totp.now()

                # 启用2FA
                enable_data = {
                    "0": {
                        "json": {"otp": otp_code, "twoFactorSecret": two_factor_secret}
                    }
                }

                async with session.post(
                    "https://targon.com/api/trpc/account.enable2FA?batch=1",
                    json=enable_data,
                    headers=headers,
                    cookies=cookies,
                ) as enable_response:

                    if enable_response.status == 200:
                        self.logger.info("2FA设置成功")
                        return True
                    else:
                        self.logger.error(f"启用2FA失败: {enable_response.status}")
                        return False

        except Exception as e:
            self.logger.error(f"2FA设置异常: {e}")
            return False

    async def get_api_keys(self, session: aiohttp.ClientSession):
        """获取API密钥"""
        try:
            if not self.session_cookie:
                self.logger.error("没有有效的登录凭证")
                return []

            self.logger.info("获取API密钥...")

            headers = {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
                "Accept": "*/*",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "sec-gpc": "1",
                "visitor-id": str(uuid.uuid4()),
            }
            headers.update(
                {"x-trpc-source": "react", "Referer": "https://targon.com/settings"}
            )
            cookies = {"auth_session": self.session_cookie}

            # 构造查询URL
            query_url = f"https://targon.com/api/trpc/keys.getApiKeys,model.getPopularModels,account.getUserBookmarks,account.getUserInterest,account.getUserSubscription,account.check2FA,account.getTaoPrice,account.getAlphaPrice,notification.getNotifications?batch=1&input=%7B%220%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%221%22%3A%7B%22json%22%3A%7B%22days%22%3A30%2C%22limit%22%3A3%7D%7D%2C%222%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%223%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%224%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%225%22%3A%7B%22json%22%3A%7B%22email%22%3A%22{self.email_address}%22%7D%7D%2C%226%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%227%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%2C%228%22%3A%7B%22json%22%3Anull%2C%22meta%22%3A%7B%22values%22%3A%5B%22undefined%22%5D%7D%7D%7D"

            async with session.get(
                query_url, headers=headers, cookies=cookies
            ) as response:

                if response.status == 200:
                    response_data = await response.json()
                    api_keys_data = response_data[0]["result"]["data"]["json"]

                    if api_keys_data:
                        self.logger.info(f"获取到 {len(api_keys_data)} 个API密钥")
                        return api_keys_data
                    else:
                        self.logger.warning("未找到API密钥")
                        return []
                else:
                    self.logger.error(f"获取密钥失败: {response.status}")
                    return []

        except Exception as e:
            self.logger.error(f"获取密钥异常: {e}")
            return []

    async def get_available_accounts(self) -> List[Dict]:
        """
        从msmail.py中获取可用的邮箱（排除已注册的和注册失败的）

        Returns:
            List[Dict]: 可用的账号列表
        """
        # 获取已注册的账号
        registered_emails = await self.get_registered_emails()
        # 获取注册失败的账号
        error_emails = await self.get_error_emails()

        # 过滤出未注册的账号且不在错误邮箱列表中的账号
        available_accounts = []
        for account in accounts:
            email = account.get("email")
            if email and email not in registered_emails and email not in error_emails:
                available_accounts.append(account)

        self.logger.info(f"找到{len(available_accounts)}个未注册的账号")
        return available_accounts

    async def save(self, email: str, password: str, api_keys: List[Dict]):
        """
        保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作

        Args:
            email: 邮箱地址
            password: 密码
            api_keys: API密钥列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 提取第一个API密钥作为主要密钥
            main_api_key = ""
            if api_keys:
                main_api_key = api_keys[0].get("key", "")

            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(
                    self.account_file, "a+", encoding="utf-8"
                ) as f:
                    # 添加manual_code到保存的信息中
                    manual_code = self.manual_code if self.manual_code else ""
                    user_info = f"{email},{password},{main_api_key},{manual_code}\n"
                    await f.write(user_info)
            self.logger.info(f"成功保存账号 {email} 到 {self.account_file}")
        except Exception as e:
            self.logger.error(f"Failed to save user {email}: {str(e)}")

    async def register_single_account(self, email: str, page):
        """注册单个账户的完整流程"""
        try:
            self.logger.info(
                f"开始注册新账户: {email} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # 设置邮箱地址
            self.email_address = email

            # 生成密码
            password = self.generate_password()
            self.logger.info(f"生成密码: {password}")

            # 1. 使用浏览器注册账户
            if not await self.register_account(page, password):
                return False

            # 2. 获取激活链接
            activation_link = await self.get_activation_link()
            if not activation_link:
                return False

            # 创建HTTP会话用于后续操作
            timeout = aiohttp.ClientTimeout(total=60)
            async with aiohttp.ClientSession(timeout=timeout) as session:

                # 3. 使用HTTP请求激活邮箱
                if not await self.activate_email(session, activation_link):
                    return False

                # 4. 使用HTTP请求设置2FA
                if not await self.setup_2fa(session):
                    return False

                # 5. 使用HTTP请求获取API密钥
                api_keys = await self.get_api_keys(session)
                if not api_keys:
                    self.logger.error("未获取到API密钥")
                    return False

                # 7. 保存账号信息
                await self.save(email, password, api_keys)

                # 8. 显示成功信息
                self.logger.info("账户注册完成!")
                self.logger.info(f"邮箱: {self.email_address}")
                self.logger.info(f"密码: {password}")
                if self.manual_code:
                    self.logger.info(f"2FA手动码: {self.manual_code}")
                self.logger.info(f"API密钥:")
                for key_info in api_keys:
                    key = key_info.get("key", "")
                    self.logger.info(
                        f"   {key[:15]}...{key[-8:] if len(key) > 23 else key}"
                    )

                return True

        except Exception as e:
            self.logger.error(f"注册流程异常: {e}")
            await self.save_error_account(email)
            return False

    async def register_accounts(self, register_count: int = 5):
        """注册多个账号"""
        registered_users = []

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        # 开始注册账号
        for i in range(min(register_count, len(available_accounts))):
            account = available_accounts[i]
            email = account.get("email")

            self.logger.info(f"Registering account {i+1}/{register_count}: {email}")

            # 为每个账号生成新的浏览器指纹
            fingerprint = self.fingerprint_generator.generate_fingerprint()
            self.logger.info(
                f"为账号 {email} 生成新的浏览器指纹: UA={fingerprint['user_agent'][:50]}..., "
                f"视口={fingerprint['viewport']}, 时区={fingerprint['timezone_id']}"
            )

            # 获取camoufox配置选项
            camoufox_options = self.fingerprint_generator.get_camoufox_options(
                fingerprint
            )

            # 启动camoufox浏览器
            async with AsyncCamoufox(**camoufox_options) as browser:
                # 创建页面
                page = await browser.new_page()

                try:
                    # 检查当前IP
                    # try:
                    #     await page.goto(
                    #         "https://myip.ipip.net/", wait_until="load", timeout=60000
                    #     )
                    #     text_content = await page.text_content("body")
                    #     self.logger.info(f"Current IP Address: {text_content}")
                    # except Exception as e:
                    #     self.logger.error(f"Failed to check IP: {str(e)}")

                    # self.logger.info(
                    #     f"Registering account {i+1}/{register_count}: {email}"
                    # )

                    # 注册账号
                    success = await self.register_single_account(email, page)

                    if success:
                        registered_users.append(email)
                        self.logger.info(
                            f"Targon account {i+1} Created Successfully: {email}"
                        )
                    else:
                        # 注册失败，保存到错误账号列表
                        await self.save_error_account(email)
                        self.logger.error(
                            f"Targon account {i+1} registration failed: {email}"
                        )

                    # 等待下一次注册
                    if i < min(register_count, len(available_accounts)) - 1:
                        wait_time = 2  # 等待10秒
                        self.logger.info(
                            f"Waiting {wait_time} seconds before registering the next account..."
                        )
                        await page.wait_for_timeout(wait_time * 1000)

                except Exception as e:
                    self.logger.error(f"注册账号 {email} 时出错: {e}")
                    await self.save_error_account(email)

        return registered_users

    async def do(self, register_count: int = 5):
        """执行注册任务"""
        registered_users = []
        success_count = 0

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        self.logger.info(f"找到 {len(available_accounts)} 个可用账号")

        try:
            users = await self.register_accounts(register_count)
            registered_users.extend(users)
            success_count += len(users)
            self.logger.info(f"Registered {len(users)}/{register_count}")
        except Exception as e:
            self.logger.error(f"Registration task failed: {str(e)}")

        self.logger.info(f"Completed: {success_count}/{register_count}")
        return registered_users
