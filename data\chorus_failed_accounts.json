[{"email": "<EMAIL>", "error": "Page.click: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"button[phx-disable-with=\\\"Confirming...\\\"]\")\n", "time": "2025-03-31 20:29:01"}, {"email": "<EMAIL>", "error": "Page.click: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"button[phx-disable-with=\\\"Confirming...\\\"]\")\n", "time": "2025-03-31 20:29:32"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 20:31:04"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 20:38:38"}, {"email": "orrin<PERSON><PERSON><PERSON>@outlook.com", "error": "无法从邮件中提取确认链接: or<PERSON><PERSON><PERSON><PERSON>@outlook.com", "time": "2025-03-31 20:40:11"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 20:51:50"}, {"email": "<EMAIL>", "error": "未收到确认邮件: <EMAIL>", "time": "2025-03-31 20:53:21"}, {"email": "<EMAIL>", "error": "未收到确认邮件: <EMAIL>", "time": "2025-03-31 20:57:33"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:12:58"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:14:45"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:15:30"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:16:42"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:18:34"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:21:57"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:22:15"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:23:09"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:23:27"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:25:18"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:25:46"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:26:02"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:26:26"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:26:41"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:27:00"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:27:19"}, {"email": "<EMAIL>", "error": "", "time": "2025-03-31 21:28:01"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 21:36:57"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:37:17"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 21:37:18"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:37:25"}, {"email": "<EMAIL>", "error": "Page.goto: net::ERR_ABORTED at https://app.chorus.sh/desktop-auth\nCall log:\n  - navigating to \"https://app.chorus.sh/desktop-auth\", waiting until \"load\"\n", "time": "2025-03-31 21:37:44"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n  navigated to \"https://app.chorus.sh/users/register\"\n============================================================", "time": "2025-03-31 21:37:57"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 21:38:12"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:38:12"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:38:41"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:39:02"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:39:22"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:40:00"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:40:04"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:40:49"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:41:08"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:41:48"}, {"email": "keeant<PERSON><EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:42:01"}, {"email": "keeant<PERSON><EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:42:32"}, {"email": "keeant<PERSON><EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:42:32"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:43:11"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n============================================================", "time": "2025-03-31 21:43:15"}, {"email": "<EMAIL>", "error": "Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"https://app.chorus.sh/dashboard\" until 'load'\n  navigated to \"https://app.chorus.sh/users/register\"\n  navigated to \"https://app.chorus.sh/users/register\"\n============================================================", "time": "2025-03-31 21:54:54"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:56:29"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:58:04"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 21:59:43"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 22:01:22"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 22:01:43"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 22:02:04"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 22:02:29"}, {"email": "<EMAIL>", "error": "无法获取token: <EMAIL>", "time": "2025-03-31 22:02:52"}, {"email": "<EMAIL>", "error": "无法从邮件中提取确认链接: <EMAIL>", "time": "2025-03-31 22:04:25"}]