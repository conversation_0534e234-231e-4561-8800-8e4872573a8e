from playwright.async_api import async_playwright, Playwright, Page, Frame, Request, Route
from pathlib import Path
import hashlib
import logging
import os
import asyncio
import ssl
from typing import Optional, Dict, Tuple
import time
import random
from urllib.parse import urlparse


logger = logging.getLogger(__name__)

# 文件类型映射表
CONTENT_TYPE_MAP: Dict[str, Tuple[str, str]] = {
    'js': ('js', 'text/javascript'),
    'css': ('css', 'text/css'),
    'png': ('img', 'image/png'),
    'jpg': ('img', 'image/jpeg'),
    'jpeg': ('img', 'image/jpeg'),
    'gif': ('img', 'image/gif'),
    'webp': ('img', 'image/webp'),
    'svg': ('img', 'image/svg+xml'),
}

# 缓存基础目录
CACHE_BASE_DIR = Path('./cache')
# 缓存有效期（秒）- 默认7天
CACHE_TTL = 7 * 24 * 60 * 60
# 最大重试次数
MAX_RETRIES = 3
# 重试延迟基础值（秒）
RETRY_DELAY_BASE = 1.0

# 确保缓存目录存在
CACHE_BASE_DIR.mkdir(parents=True, exist_ok=True)

async def handle_route(route: Route, request: Request) -> None:
    """
    处理路由请求，缓存js、css和图片文件，仅在响应成功时缓存
    
    Args:
        route: Playwright路由对象
        request: 请求对象
    """
    # await route.continue_()
    # return
    try:
        url = request.url
        
        # 跳过数据URI
        if url.startswith('data:'):
            await route.continue_()
            return
            
        # 获取文件扩展名 - 使用更健壮的方法
        parsed_url = urlparse(url)
        path = parsed_url.path
        file_extension = path.split('.')[-1].lower() if '.' in path else None
        
        # 检查是否为需要缓存的文件类型
        if file_extension in CONTENT_TYPE_MAP:
            await handle_cacheable_resource(route, request, url, file_extension)
        else:
            # 非缓存类型文件直接放行
            await route.continue_()
            
    except Exception as e:
        logger.error(f"处理路由时出错: {str(e)}, URL: {request.url}")
        # 出错时尝试继续原始请求，确保页面不会因为缓存错误而无法加载
        try:
            await route.continue_()
        except Exception as fallback_error:
            logger.error(f"回退继续请求时出错: {str(fallback_error)}")

async def handle_cacheable_resource(route: Route, request: Request, url: str, file_extension: str) -> None:
    """处理可缓存资源"""
    try:
        file_type, content_type = CONTENT_TYPE_MAP[file_extension]
        
        # 创建缓存路径
        cache_path = create_cache_path(url, file_type)
        
        # 检查缓存是否存在且未过期
        if cache_path.exists() and not is_cache_expired(cache_path):
            # 缓存存在且未过期，直接返回缓存内容
            await serve_from_cache(route, cache_path, content_type)
        else:
            # 缓存不存在或已过期，获取并可能缓存
            await fetch_and_cache(route, cache_path, content_type)
            
    except Exception as e:
        logger.error(f"处理可缓存资源时出错: {str(e)}, URL: {url}")
        await route.continue_()  # 确保请求继续

def create_cache_path(url: str, file_type: str) -> Path:
    """创建并返回缓存文件路径"""
    # 创建分类缓存目录
    cache_dir = CACHE_BASE_DIR / file_type
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成缓存文件名
    url_hash = hashlib.md5(url.encode()).hexdigest()
    
    # 从URL中提取文件名
    parsed_url = urlparse(url)
    path = parsed_url.path
    file_name = os.path.basename(path)
    
    # 处理文件名为空的情况
    if not file_name:
        file_name = f"{url_hash}.{file_type}"
    
    # 处理文件名过长的情况
    if len(file_name) > 50:
        file_name = f"{url_hash}_{file_name[-40:]}"
    else:
        file_name = f"{url_hash}_{file_name}"
    
    # 确保文件名是有效的
    file_name = sanitize_filename(file_name)
    
    return cache_dir / file_name

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不合法字符"""
    # 替换Windows和Unix系统都不允许的文件名字符
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def is_cache_expired(cache_path: Path) -> bool:
    """检查缓存是否过期"""
    if not cache_path.exists():
        return True
        
    # 获取文件最后修改时间
    mtime = cache_path.stat().st_mtime
    # 当前时间
    now = time.time()
    # 如果文件修改时间 + TTL < 当前时间，则缓存过期
    return (mtime + CACHE_TTL) < now

async def serve_from_cache(route: Route, cache_path: Path, content_type: str) -> None:
    """从缓存提供资源"""
    try:
        body = cache_path.read_bytes()
        # 添加缓存控制头
        headers = {
            "Cache-Control": f"max-age={CACHE_TTL}",
            "X-Cache": "HIT"
        }
        await route.fulfill(body=body, content_type=content_type, headers=headers)
        logger.debug(f"从缓存提供: {cache_path}")
    except Exception as e:
        logger.error(f"从缓存提供资源失败: {str(e)}, 路径: {cache_path}")
        await route.continue_()  # 失败时继续原始请求

async def fetch_and_cache(route: Route, cache_path: Path, content_type: str) -> None:
    """获取并缓存资源，带有重试机制"""
    retries = 0
    last_error = None
    
    while retries < MAX_RETRIES:
        try:
            # 设置请求超时
            response = await asyncio.wait_for(route.fetch(), timeout=15.0)
            
            # 只有当响应成功时才缓存
            if 200 <= response.status < 300:
                try:
                    body = await response.body()
                    # 使用临时文件写入，然后重命名，避免部分写入问题
                    temp_path = cache_path.with_suffix('.tmp')
                    temp_path.write_bytes(body)
                    temp_path.rename(cache_path)
                    logger.debug(f"已缓存: {cache_path}")
                except Exception as cache_error:
                    logger.error(f"缓存资源失败: {str(cache_error)}")
                    # 缓存失败不影响响应返回
            
            # 添加自定义头
            headers = dict(response.headers)
            headers["X-Cache"] = "MISS"
            
            # 无论是否缓存都返回原始响应
            await route.fulfill(
                status=response.status,
                headers=headers,
                body=await response.body(),
                content_type=response.headers.get("content-type", content_type)
            )
            return  # 成功完成
            
        except (ssl.SSLError, asyncio.TimeoutError) as e:
            last_error = e
            retries += 1
            
            # SSL错误特别处理
            # if isinstance(e, ssl.SSLError) or "SSL" in str(e) or "TLS" in str(e) or "EPROTO" in str(e):
            #     logger.warning(f"SSL/TLS连接错误 (尝试 {retries}/{MAX_RETRIES}): {str(e)}")
            # else:
            #     logger.warning(f"请求超时 (尝试 {retries}/{MAX_RETRIES}): {str(e)}")
                
            # 指数退避策略
            if retries < MAX_RETRIES:
                delay = RETRY_DELAY_BASE * (2 ** (retries - 1)) * (0.5 + random.random())
                #logger.info(f"等待 {delay:.2f} 秒后重试...")
                await asyncio.sleep(delay)
        except Exception as e:
            logger.error(f"获取资源失败: {str(e)}")
            await route.continue_()
            return
    
    # 所有重试都失败了
    logger.error(f"在 {MAX_RETRIES} 次尝试后获取资源失败: {str(last_error)}")
    await route.continue_()
    
    
    
async def setup_page_caching(route: Route, request: Request):
    url = request.url
    try:
        # 获取原始响应
        response = await route.fetch()
        
        # 创建新的响应头
        headers = dict(response.headers)
        # 添加强缓存控制头到响应
        headers['Cache-Control'] = 'public, max-age=31536000, immutable'
        headers['Expires'] = 'Thu, 31 Dec 2037 23:59:59 GMT'
        
        # 用修改后的响应头完成请求
        await route.fulfill(
            status=response.status,
            headers=headers,
            body=await response.body()
        )
    except Exception as error:
        logger.error(f"资源缓存失败: {url} - {error}")
        await route.continue_()