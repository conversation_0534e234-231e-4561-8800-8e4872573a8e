import json
import logging
import os
from pathlib import Path
import re
import signal
import sys
import time
from typing import Union, List, Dict, Optional, Set
import aiofiles
import asyncio
import random
from playwright.async_api import (
    async_playwright,
    Playwright,
    Page,
    Frame,
    Request,
    Route,
)
from utils.cache_helper import handle_route, setup_page_caching
from utils.config_manager import ConfigManager
from utils.user_generator import UserGenerator
from utils.captcha_helper import CaptchaHelper
from utils.msmail import accounts, get_email_content
from urllib.parse import urlparse, parse_qs


# 定义自定义异常类
class IPBadError(Exception):
    """自定义的异常类"""

    def __init__(self, message="IPBadError"):
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        return f"IPBadError: {self.message}"


class PromptLayerRegisterTask:
    """
    PromptLayer注册任务
    """

    def __init__(
        self,
        config_manager: ConfigManager,
        account_file: Union[str, Path] = "data/promptlayer.csv",
        error_file: Union[str, Path] = "data/promptlayer_error_accounts.txt",
    ) -> None:
        """
        初始化配置管理器

        Args:
            config_manager: 配置管理
            account_file: 账号文件
            error_file: 注册失败的账号文件
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.error_file = Path(error_file)
        self.user_generator = UserGenerator(config_manager)
        self.browser_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.file_lock = asyncio.Lock()
        self.captcha_helper = CaptchaHelper(
            two_captcha_key=self.config_manager.two_captcha_key,
            captcharun_token=self.config_manager.captcharun_token,
            browser_user_agent=self.browser_user_agent,
        )
        self.password = "Dean0104@promptlayer"

    async def get_registered_emails(self) -> Set[str]:
        """
        读取已经注册的账号，避免重复注册

        Returns:
            Set[str]: 已注册的邮箱集合
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            # 检查CSV文件是否存在
            if not self.account_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.account_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return set()

            # 读取并解析CSV文件
            async with aiofiles.open(self.account_file, "r", encoding="utf-8") as f:
                content = await f.read()

            # 提取邮箱（CSV的第一列）
            emails = set()
            for line in content.strip().split("\n"):
                if line.strip():
                    email = line.split(",")[0]
                    emails.add(email.strip())

            self.logger.info(f"已从{self.account_file}读取{len(emails)}个已注册账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取已注册账号时出错: {str(e)}")
            return set()

    async def get_error_emails(self) -> List[str]:
        """
        读取注册失败的错误账号，避免重复注册

        Returns:
            List[str]: 注册失败的邮箱列表
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            # 检查错误账号文件是否存在
            if not self.error_file.exists():
                # 如果文件不存在，创建一个空文件
                async with aiofiles.open(self.error_file, "w", encoding="utf-8") as f:
                    await f.write("")
                return []

            # 读取并解析文件
            async with aiofiles.open(self.error_file, "r", encoding="utf-8") as f:
                content = await f.read()

            emails = [
                line.strip() for line in content.strip().split("\n") if line.strip()
            ]

            self.logger.info(f"已从{self.error_file}读取{len(emails)}个注册失败的账号")
            return emails
        except Exception as e:
            self.logger.error(f"读取注册失败账号时出错: {str(e)}")
            return []

    async def save_error_account(self, email: str) -> None:
        """
        保存注册失败的账号到文件

        Args:
            email: 注册失败的邮箱
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.error_file), exist_ok=True)

            async with self.file_lock:
                async with aiofiles.open(self.error_file, "a", encoding="utf-8") as f:
                    await f.write(f"{email}\n")
            self.logger.info(f"成功保存注册失败账号 {email} 到 {self.error_file}")
        except Exception as e:
            self.logger.error(f"保存注册失败账号时出错: {str(e)}")

    async def save(self, email: str, api_key: str) -> None:
        """
        保存用户信息到文件，使用aiofiles和锁机制确保并行环境下的安全操作

        Args:
            email: 邮箱地址
            api_key: API密钥
        """
        try:
            # 确保data目录存在
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)

            async with self.file_lock:  # 获取锁，确保同一时间只有一个任务能写文件
                async with aiofiles.open(
                    self.account_file, "a+", encoding="utf-8"
                ) as f:
                    user_info = f"{email},{api_key}\n"
                    await f.write(user_info)
            self.logger.info(f"成功保存账号 {email} 到 {self.account_file}")
        except Exception as e:
            self.logger.error(f"Failed to save user {email}: {str(e)}")

    async def get_available_accounts(self) -> List[Dict]:
        """
        从msmail.py中获取可用的邮箱（排除已注册的和注册失败的）

        Returns:
            List[Dict]: 可用的账号列表
        """
        # 获取已注册的账号
        registered_emails = await self.get_registered_emails()
        # 获取注册失败的账号
        error_emails = await self.get_error_emails()

        # 过滤出未注册的账号且不在错误邮箱列表中的账号
        available_accounts = []
        for account in accounts:
            email = account.get("email")
            if email and email not in registered_emails and email not in error_emails:
                available_accounts.append(account)

        self.logger.info(f"找到{len(available_accounts)}个未注册的账号")
        return available_accounts

    async def register_single_account(self, email: str, page: Page) -> Dict:
        """
        注册单个PromptLayer账号

        Args:
            email: 邮箱地址
            page: 浏览器页面对象

        Returns:
            Dict: 包含注册结果的字典，成功时包含API密钥
        """
        self.logger.info(f"开始注册PromptLayer账号: {email}")

        # 提取邮箱前缀作为用户名
        email_prefix = email.split("@")[0]
        name = email_prefix

        # 打开PromptLayer注册页面
        await page.goto("https://dashboard.promptlayer.com/create-account", timeout=60000)
        await page.wait_for_load_state("networkidle")

        # 填写邮箱
        await page.fill('input[type="text"][placeholder="Email"][id="email"][name="email"]', email)

        # 填写用户名
        await page.fill('input[type="text"][id="name"][name="name"]', name)

        # 填写密码
        await page.fill('input[type="password"][placeholder="Password"][id="password"][name="password"]', self.password)

        # 填写确认密码
        await page.fill('input[type="password"][placeholder="Re-enter Password"][id="verifyPassword"][name="verifyPassword"]', self.password)

        

        # 点击创建账号按钮
        await page.click('button[type="submit"]')

        # 等待注册完成，可能会跳转到仪表板页面
        try:
            await page.wait_for_url("https://dashboard.promptlayer.com/workspace/**", timeout=30000)
            self.logger.info(f"PromptLayer账号注册成功: {email}")

            # 获取工作区ID
            #workspace_id = await self.get_workspace_id(page)

            # 发送onboarding请求
            await self.send_onboarding_requests(page, name)

            # 创建API密钥
            api_key = "pl_mieyou123"
            #api_key = await self.create_api_key(page, workspace_id)

            return {
                "success": True,
                "email": email,
                "api_key": api_key
            }
        except Exception as e:
            self.logger.error(f"注册失败或超时: {str(e)}")
            await self.save_error_account(email)
            return {
                "success": False,
                "email": email,
                "error": str(e)
            }

    async def get_workspace_id(self, page: Page) -> str:
        """
        获取工作区ID

        Args:
            page: 浏览器页面对象

        Returns:
            str: 工作区ID
        """
        self.logger.info("获取工作区ID...")
        
        # 等待页面加载完成
        await page.wait_for_load_state("networkidle")
        
        # 从页面中提取工作区ID
        workspace_id = await page.evaluate("""
            () => {
                // 尝试从localStorage或全局变量中获取工作区ID
                try {
                    // 检查localStorage
                    const storageData = localStorage.getItem('ACTIVE_WORKSPACE_ID');
                    if (storageData) {
                        return storageData;
                    }
                } catch (e) {
                    console.error('Error extracting workspace ID:', e);
                }
                return null;
            }
        """)

        self.logger.info(f"成功获取工作区ID: {workspace_id}")
        return workspace_id

    async def send_onboarding_requests(self, page: Page, name: str) -> None:
        """
        发送onboarding请求

        Args:
            page: 浏览器页面对象
            name: 用户名（用于company_name）
        """
        self.logger.info("发送onboarding请求...")

        # 等待页面加载完成
        await page.wait_for_load_state("networkidle")

        # 请求1: reasons_to_use_promptlayer
        await page.evaluate("""
            async (name) => {
                try {
                    // 获取认证令牌
                    const token = localStorage.getItem('ACCESS_TOKEN');
                    if (!token) return;

                    // 请求1: reasons_to_use_promptlayer
                    await fetch("https://api.promptlayer.com/user/onboarding", {
                        method: "POST",
                        headers: {
                            "accept": "*/*",
                            "authorization": `Bearer ${token}`,
                            "content-type": "application/json"
                        },
                        body: JSON.stringify({
                            onboarding_question_key: "reasons_to_use_promptlayer",
                            response: {
                                reasons: ["observability"]
                            }
                        })
                    });

                    // 请求2: about_user
                    await fetch("https://api.promptlayer.com/user/onboarding", {
                        method: "POST",
                        headers: {
                            "accept": "*/*",
                            "authorization": `Bearer ${token}`,
                            "content-type": "application/json"
                        },
                        body: JSON.stringify({
                            onboarding_question_key: "about_user",
                            response: {
                                about_user: {
                                    role: "developer",
                                    company_size: "startup",
                                    company_name: name
                                }
                            }
                        })
                    });

                    // 请求3: sound_like_you
                    await fetch("https://api.promptlayer.com/user/onboarding", {
                        method: "POST",
                        headers: {
                            "accept": "*/*",
                            "authorization": `Bearer ${token}`,
                            "content-type": "application/json"
                        },
                        body: JSON.stringify({
                            onboarding_question_key: "sound_like_you",
                            response: {
                                sound_like_you: "expert"
                            }
                        })
                    });

                    console.log("成功发送所有onboarding请求");
                } catch (e) {
                    console.error("发送onboarding请求时出错:", e);
                }
            }
        """, name)

        self.logger.info("onboarding请求已发送")

    async def create_api_key(self, page: Page, workspace_id: str) -> str:
        """
        创建API密钥

        Args:
            page: 浏览器页面对象
            workspace_id: 工作区ID

        Returns:
            str: API密钥
        """
        self.logger.info("创建API密钥...")

        # 等待页面加载完成
        await page.wait_for_load_state("networkidle")

        # 创建API密钥
        api_key = await page.evaluate("""
            async (workspace_id) => {
                try {
                    // 获取认证令牌
                    const token = localStorage.getItem('ACCESS_TOKEN');
                    if (!token) return null;

                    // 创建API密钥
                    const response = await fetch("https://api.promptlayer.com/create-api-key", {
                        method: "POST",
                        headers: {
                            "accept": "*/*",
                            "authorization": `Bearer ${token}`,
                            "content-type": "application/json"
                        },
                        body: JSON.stringify({ workspace_id: workspace_id })
                    });

                    const result = await response.json();
                    if (result.success && result.api_key) {
                        return result.api_key;
                    }

                    return null;
                } catch (e) {
                    console.error("创建API密钥时出错:", e);
                    return null;
                }
            }
        """, workspace_id)

        if not api_key:
            raise Exception("无法创建API密钥")

        self.logger.info(f"成功创建API密钥: {api_key}")
        return api_key

    async def register_with_proxy(
        self,
        playwright: Playwright,
        proxy_info: Optional[Dict],
        register_count: int = 5,
    ):
        """使用指定代理注册多个账号"""
        registered_users = []

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        # 配置浏览器选项
        browser_options = {
            "headless": False,  # 设置为True可以在无头模式下运行
        }

        # 配置上下文选项
        context_options = {
            "viewport": {"width": 1280, "height": 800},
            "user_agent": self.browser_user_agent,
        }

        # 如果提供了代理信息，配置代理
        proxy_info_str = ""
        if proxy_info:
            proxy_config = {
                "server": proxy_info["host"],
            }

            # 添加代理认证（如果提供）
            if proxy_info.get("username") and proxy_info.get("password"):
                proxy_config["username"] = proxy_info["username"]
                proxy_config["password"] = proxy_info["password"]

            context_options["proxy"] = proxy_config
            proxy_info_str = f" with proxy {proxy_info['host']}"

        # 启动浏览器并创建上下文
        browser = await playwright.chromium.launch(**browser_options)
        context = await browser.new_context(**context_options)

        # 添加路由处理
        await context.route("**/*", handle_route)
        page = await context.new_page()

        try:
            # 检查当前IP
            try:
                await page.goto(
                    "https://myip.ipip.net/", wait_until="load", timeout=60000
                )
                text_content = await page.text_content("body")
                self.logger.info(f"Current IP Address: {text_content}")
            except Exception as e:
                self.logger.error(f"Failed to check IP{proxy_info_str}: {str(e)}")

            # 开始注册账号
            for i in range(min(register_count, len(available_accounts))):
                try:
                    account = available_accounts[i]
                    email = account.get("email")

                    self.logger.info(
                        f"Registering account {i+1}/{register_count}{proxy_info_str}: {email}"
                    )
                    # 清除cookies
                    await context.clear_cookies()

                    # 注册账号
                    result = await self.register_single_account(email, page)

                    if result["success"]:
                        # 保存账号信息
                        await self.save(email, result["api_key"])
                        registered_users.append(email)
                        self.logger.info(
                            f"PromptLayer account {i+1} Created Successfully{proxy_info_str}: {email}"
                        )

                    # 等待下一次注册
                    if i < min(register_count, len(available_accounts)) - 1:
                        wait_time = self.config_manager.get("register_interval", 20)
                        self.logger.info(
                            f"Waiting {wait_time} seconds before registering the next account..."
                        )
                        await page.wait_for_timeout(wait_time * 1000)
                except IPBadError as e:
                    self.logger.error(f"IP被封禁，停止注册: {str(e)}")
                    break
                except Exception as e:
                    self.logger.error(
                        f"Registration failed for account {i+1}{proxy_info_str}: {str(e)}"
                    )
                    await page.wait_for_timeout(5000)  # 等待一段时间再继续
        finally:
            await page.close()
            await context.close()
            await browser.close()

        return registered_users

    async def do(
        self,
        playwright: Playwright,
        register_count: int = 5,
        max_concurrent_tasks: int = 1,
    ):
        """执行注册任务"""
        registered_users = []
        success_count = 0
        proxies = self.config_manager.get("proxies", [])
        self.logger.info(f"Total proxies: {len(proxies)}")
        sem = asyncio.Semaphore(max_concurrent_tasks)

        # 获取可用账号
        available_accounts = await self.get_available_accounts()
        if not available_accounts:
            self.logger.info("没有可用的账号，跳过注册")
            return registered_users

        self.logger.info(f"找到 {len(available_accounts)} 个可用账号")

        async def register_task(proxy_info: Optional[Dict], task_id: str):
            nonlocal success_count
            self.logger.info(f"Starting task {task_id}")
            proxy_desc = f"proxy {proxy_info['host']}" if proxy_info else "no proxy"

            try:
                async with sem:
                    users = await self.register_with_proxy(
                        playwright, proxy_info, register_count
                    )
                    registered_users.extend(users)
                    success_count += len(users)
                    self.logger.info(
                        f"Task {task_id}: Registered {len(users)}/{register_count} with {proxy_desc}"
                    )
                    return users
            except asyncio.CancelledError:
                self.logger.info(f"Task {task_id} was cancelled")
                raise  # 传播取消信号以确保资源清理
            except Exception as e:
                self.logger.error(f"Task {task_id} failed with {proxy_desc}: {str(e)}")
                return []

        # 创建任务列表
        # tasks_def = [("no_proxy", None)] + [(f"proxy_{i}", p) for i, p in enumerate(proxies, 1)]
        # tasks_def = [(f"proxy_{i}", p) for i, p in enumerate(proxies, 1)]
        tasks_def = [("no_proxy", None)] + [(f"no_proxy_{i}", None) for i in range(0)]
        total_planned = register_count * len(tasks_def)

        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 定义信号处理函数
        def handle_sigint():
            """处理Ctrl+C信号"""
            self.logger.info("\nReceived termination signal, cancelling tasks...")
            for task in tasks:
                if not task.done():
                    task.cancel()

        # 注册信号处理器（兼容Windows）
        if sys.platform == "win32":
            # Windows使用默认信号处理
            signal.signal(
                signal.SIGINT, lambda s, f: loop.call_soon_threadsafe(handle_sigint)
            )
        else:
            # Unix系统使用add_signal_handler
            loop.add_signal_handler(signal.SIGINT, handle_sigint)

        try:
            # 创建并跟踪所有任务
            tasks = [
                loop.create_task(register_task(proxy, tid)) for tid, proxy in tasks_def
            ]
            await asyncio.gather(*tasks, return_exceptions=False)
        except asyncio.CancelledError:
            self.logger.info("Main execution was cancelled")
        finally:
            # 清理信号处理器
            if sys.platform != "win32":
                loop.remove_signal_handler(signal.SIGINT)

        self.logger.info(f"Completed: {success_count}/{total_planned}")
        return registered_users