import asyncio
import logging
from pathlib import Path
import random
import time
from typing import Union
from utils.config_manager import ConfigManager
from utils.user_generator import UserGenerator
from utils.mail_verification_helper import MailVerificationHelper
import aiohttp
from utils.captcha_helper import CaptchaHelper


class KlusterRegisterTask:
    """
    Kluster平台注册任务
    """
    def __init__(
        self,
        config_manager: ConfigManager,
        account_file: Union[str, Path] = "kluster_accounts.csv",
        token_file: Union[str, Path] = "kluster_tokens.csv"
    ) -> None:
        """
        初始化配置管理器
        
        Args:
            config_manager: 配置管理
            account_file: 账号文件
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        self.account_file = Path(account_file)
        self.token_file = Path(token_file)
        self.user_generator = UserGenerator(config_manager)
        self.current_user = None
        self.SCRAPELESS_API_KEY = config_manager.scrapeless_api_key
        self.captcha_helper = CaptchaHelper(
            scrapeless_api_key=config_manager.scrapeless_api_key
        )

    def saveAccount(self,email:str, user: dict):
        """保存用户信息到文件"""
        with open(self.account_file, 'a+', encoding='utf-8') as f:
            user_info = f"{email},{user['password']},{user['first_name']},{user['last_name']}\n"
            f.write(user_info)

    def saveToken(self,email:str, token:str):
        """保存用户信息到文件"""
        with open(self.token_file, 'a+', encoding='utf-8') as f:
            user_info = f"{email},{token}\n"
            f.write(user_info)

    async def do(self, count: int = 1):
        """
        执行注册任务
        
        Args:
            count: 要注册的账号数量
        """
        success_count = 0
        fail_count = 0
        
        for i in range(count):
            self.logger.info(f"开始注册第 {i+1}/{count} 个账号")
            try:
                self.current_user = self.user_generator.generate_user()
                headers = {
                    'authority': 'api.kluster.ai',
                    'accept': 'application/json, text/plain, */*',
                    'accept-encoding': 'gzip, deflate, br, zstd',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'content-type': 'application/json',
                    'origin': 'https://platform.kluster.ai',
                    'referer': 'https://platform.kluster.ai/',
                    'sec-ch-ua': '"Chromium";v="134", "Not A Brand";v="24", "Google Chrome";v="134"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
                
                # proof_domain = "godgodgame.com"
                # proof_api = "http://**************:89/api"
                proof_domain = "igiven.com"
                proof_api = "http://**************:90/api"
                proof_email = f"{self.current_user['email_prefix']}@{proof_domain}"
                
                self.logger.info(f"Registering with email: {proof_email}, first_name: {self.current_user['first_name']},last_name: {self.current_user['last_name']}")
                
                data = {
                    "captcha": await self.captcha_helper.get_turnstile_token(
                        page_url="https://platform.kluster.ai/signup",
                        site_key="0x4AAAAAAA6RVF_1pdlJ0JJF"
                    ),
                    "email": proof_email,
                    "password": self.current_user['password'],
                    "firstName": self.current_user['first_name'],
                    "lastName": self.current_user['last_name']
                }
                
                timestamp = int(time.time()-20)
                async with aiohttp.ClientSession() as session:
                    async with session.post("https://api.kluster.ai/v1/user/register", headers=headers, json=data) as response:
                        if response.ok:
                            self.saveAccount(proof_email, self.current_user)
                            self.logger.info("Registration successful")
                            cookies = response.cookies
                            
                            # 获取验证链接并验证
                            verification_code = await MailVerificationHelper.get_kluster_verification_code(
                                proof_api,
                                proof_email, 
                                timestamp
                            )
                            
                            # 验证邮箱
                            async with session.post(
                                "https://api.kluster.ai/v1/user/validateEmail",
                                cookies=cookies,
                                json={"verificationCode": verification_code}
                            ) as validate_response:
                                if validate_response.status == 200:
                                    self.logger.info(f"Email validation successful with code: {verification_code}")
                                    
                                    # 获取API密钥
                                    async with session.post(
                                        "https://api.kluster.ai/v1/user/apiKeys",
                                        cookies=cookies,
                                        json={"name": "Bulk Key"}
                                    ) as api_response:
                                        if api_response.status == 200:
                                            api_data = await api_response.json()
                                            api_key = api_data["key"]
                                            self.saveToken(proof_email, api_key)
                                            self.logger.info("API key obtained and saved successfully")
                                        else:
                                            error_text = await api_response.text()
                                            raise Exception(f"Failed to get API key: {error_text}")
                                else:
                                    error_text = await validate_response.text()
                                    raise Exception(f"Email validation failed: {error_text}")
                        else:
                            error_text = await response.text()
                            raise Exception(f"Registration failed: {error_text}")
                
                success_count += 1
                self.logger.info(f"第 {i+1} 个账号注册成功")
                
                # 随机延迟5-10秒，避免请求过于频繁
                await asyncio.sleep(random.uniform(5, 10))
                
            except Exception as e:
                fail_count += 1
                self.logger.error(f"第 {i+1} 个账号注册失败: {str(e)}")
                continue
        
        self.logger.info(f"注册任务完成. 成功: {success_count}, 失败: {fail_count}")
        return {
            "success": success_count,
            "failed": fail_count,
            "total": count
        }
