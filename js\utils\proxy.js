import { Proxy<PERSON><PERSON>, Agent, setGlobalDispatcher, fetch } from 'undici';

export async function getProxyAgent(maxRetries = 30) {
    let retryCount = 0;

    while (retryCount < maxRetries) {
        try {
            const proxyResponse = await fetch(
                'http://api.xiequ.cn/VAD/GetIp.aspx?act=get&uid=157446&vkey=C8C28025450777B1E9E2F1044829C0F7&num=1&time=30&plat=0&re=1&type=0&so=1&ow=1&spl=1&addr=&db=1'
            );

            const proxyData = await proxyResponse.json();

            if (proxyData.code === 0 && proxyData.data && proxyData.data.length > 0) {
                const proxyInfo = proxyData.data[0];
                const proxyUrl = `http://${proxyInfo.IP}:${proxyInfo.Port}`;
                console.log(`获取代理: ${proxyUrl} (${proxyInfo.IpAddress})`);

                const controller = new AbortController();
                const timeout = setTimeout(() => {
                    controller.abort();
                }, 10000); // 5秒超时
                // Test the proxy
                try {
                    var proxyAgent = new ProxyAgent({
                        uri: proxyUrl,
                        // 头部超时（毫秒）
                        headersTimeout: 10000, //感觉没啥用处
                        // 整体请求超时（毫秒）
                        bodyTimeout: 10000, //感觉没啥用处
                    });
                    const testResponse = await fetch('https://ifconfig.me/ip', {
                        dispatcher: proxyAgent,
                        signal: controller.signal
                    });

                    if (testResponse.ok) {
                        const ip = await testResponse.text();
                        console.log('代理IP测试成功:', ip);
                        return proxyAgent;
                    }
                } catch (error) {
                    console.log('代理IP测试失败:', error.message);
                } finally {
                    clearTimeout(timeout);
                }
            }

            retryCount++;
            console.log(`获取代理失败，第 ${retryCount} 次重试...`);
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试

        } catch (error) {
            retryCount++;
            console.log(`获取代理出错，第 ${retryCount} 次重试:`, error.message);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    throw new Error(`获取代理IP失败，已重试 ${maxRetries} 次`);
}
