import { accounts } from './utils/msmail.js';
import fs from 'fs/promises';
import { ProxyA<PERSON>, Agent, setGlobalDispatcher, fetch } from 'undici';
import { getProxyAgent } from './utils/proxy.js';
import { getCaptchaRunToken,getTwoCaptchaTokenV2 } from './utils/captcha.js';
const siteKey =  "6LcZnEErAAAAALNpzeT9jYDPVK0ge1Yd_MEY7cE6";
const siteReferer ="https://dashboard.promptlayer.com/"
const siteAction ="createAccount"
const password = "Dean0104@promptlayer";
const CSV_FILE_PATH = 'data/promptlayer.csv';
const ERROR_FILE_PATH = 'data/promptlayer_error_accounts.txt';
let directAgent = new Agent();
let proxyAgent = directAgent;

/**
 * 读取已经注册的账号，避免重复注册
 * @returns {Promise<Set<string>>} 已注册的邮箱集合
 */
async function getRegisteredEmails() {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        // 检查CSV文件是否存在
        try {
            await fs.access(CSV_FILE_PATH);
        } catch (error) {
            // 如果文件不存在，创建一个空文件
            await fs.writeFile(CSV_FILE_PATH, '');
            return new Set();
        }

        // 读取并解析CSV文件
        const content = await fs.readFile(CSV_FILE_PATH, 'utf-8');
        const lines = content.trim().split('\n');

        // 提取邮箱（CSV的第一列）
        const emails = new Set();
        for (const line of lines) {
            if (line.trim()) {
                const [email] = line.split(',');
                emails.add(email.trim());
            }
        }

        console.log(`已从${CSV_FILE_PATH}读取${emails.size}个已注册账号`);
        return emails;
    } catch (error) {
        console.error(`读取已注册账号时出错: ${error.message}`);
        return new Set();
    }
}

/**
 * 读取注册失败的错误账号，避免重复注册
 * @returns {Promise<string[]>} 注册失败的邮箱数组
 */
async function getErrorEmails() {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        // 检查错误账号文件是否存在
        try {
            await fs.access(ERROR_FILE_PATH);
        } catch (error) {
            // 如果文件不存在，创建一个空文件
            await fs.writeFile(ERROR_FILE_PATH, '');
            return [];
        }

        // 读取并解析文件
        const content = await fs.readFile(ERROR_FILE_PATH, 'utf-8');
        const emails = content.trim().split('\n')
            .filter(line => line.trim())
            .map(line => line.trim());

        console.log(`已从${ERROR_FILE_PATH}读取${emails.length}个注册失败的账号`);
        return emails;
    } catch (error) {
        console.error(`读取注册失败账号时出错: ${error.message}`);
        return [];
    }
}

/**
 * 保存注册失败的账号到文件
 * @param {string} email 注册失败的邮箱
 */
async function saveErrorAccount(email) {
    try {
        // 确保data目录存在
        await fs.mkdir('data', { recursive: true });

        await fs.appendFile(ERROR_FILE_PATH, email + '\n');
        console.log(`成功保存注册失败账号 ${email} 到 ${ERROR_FILE_PATH}`);
    } catch (error) {
        console.error(`保存注册失败账号时出错: ${error.message}`);
    }
}

/**
 * 保存账号信息到CSV文件
 * @param {string} email 邮箱
 * @param {string} apiKey API密钥
 */
async function saveAccount(email, apiKey) {
    try {
        const data = `${email},${apiKey}\n`;
        await fs.appendFile(CSV_FILE_PATH, data);
        console.log(`成功保存账号 ${email} 到 ${CSV_FILE_PATH}`);
    } catch (error) {
        console.error(`保存账号时出错: ${error.message}`);
    }
}

/**
 * 创建PromptLayer账号
 * @param {string} email 邮箱
 * @param {string} name 用户名
 * @returns {Promise<{access_token: string, user: object}>} 注册结果
 */
async function createAccount(email, name,recaptcha_response) {
    try {
        const response = await fetch("https://api.promptlayer.com/create-account", {
            method: "POST",
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "Referer": "https://dashboard.promptlayer.com/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            body: JSON.stringify({
                email: email,
                password: password,
                name: name,
                recaptcha_response:recaptcha_response
            }),
            dispatcher: proxyAgent
        });

        if (!response.ok) {
            throw new Error(`创建账号失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`成功创建账号: ${email}`);
        return result;
    } catch (error) {
        console.error(`创建账号时出错: ${error.message}`);
        throw error;
    }
}

async function getAccessToken(email) {

    try {
        const response = await fetch("https://api.promptlayer.com/login", {
            method: "POST",
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "Referer": "https://dashboard.promptlayer.com/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            body: JSON.stringify({
                email: email,
                password: password,
            })
        });

        if (!response.ok) {
            throw new Error(`登录账号失败: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        console.log(`成功登录账号: ${email}`);
        return result;
    } catch (error) {
        console.error(`创建账号时出错: ${error.message}`);
        throw error;
    }
}


/**
 * 获取工作区ID
 * @param {string} accessToken 访问令牌
 * @returns {Promise<number>} 工作区ID
 */
async function getWorkspaceId(accessToken) {
    try {
        const response = await fetch("https://api.promptlayer.com/workspaces", {
            method: "GET",
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "authorization": `Bearer ${accessToken}`,
                "cache-control": "no-cache",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "Referer": "https://dashboard.promptlayer.com/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            dispatcher: proxyAgent
        });

        if (!response.ok) {
            throw new Error(`获取工作区失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        if (!result.success || !result.workspaces || result.workspaces.length === 0) {
            throw new Error('未找到工作区');
        }

        const workspaceId = result.workspaces[0].id;
        console.log(`获取到工作区ID: ${workspaceId}`);
        return workspaceId;
    } catch (error) {
        console.error(`获取工作区ID时出错: ${error.message}`);
        throw error;
    }
}

/**
 * 发送onboarding请求
 * @param {string} accessToken 访问令牌
 * @param {string} name 用户名（用于company_name）
 * @returns {Promise<void>}
 */
async function sendOnboardingRequests(accessToken, name) {
    try {
        // 通用请求头
        const headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "authorization": `Bearer ${accessToken}`,
            "cache-control": "no-cache",
            "content-type": "application/json",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "Referer": "https://dashboard.promptlayer.com/",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        };

        // 请求1: reasons_to_use_promptlayer
        await fetch("https://api.promptlayer.com/user/onboarding", {
            method: "POST",
            headers,
            body: JSON.stringify({
                onboarding_question_key: "reasons_to_use_promptlayer",
                response: {
                    reasons: ["observability"]
                }
            }),
            dispatcher: proxyAgent
        });

        // 请求2: about_user (使用email前缀作为company_name)
        await fetch("https://api.promptlayer.com/user/onboarding", {
            method: "POST",
            headers,
            body: JSON.stringify({
                onboarding_question_key: "about_user",
                response: {
                    about_user: {
                        role: "developer",
                        company_size: "startup",
                        company_name: name
                    }
                }
            }),
            dispatcher: proxyAgent
        });

        // 请求3: sound_like_you
        await fetch("https://api.promptlayer.com/user/onboarding", {
            method: "POST",
            headers,
            body: JSON.stringify({
                onboarding_question_key: "sound_like_you",
                response: {
                    sound_like_you: "expert"
                }
            }),
            dispatcher: proxyAgent
        });

        console.log("成功发送所有onboarding请求");
    } catch (error) {
        console.error(`发送onboarding请求时出错: ${error.message}`);
        // 继续执行，不中断注册流程
    }
}

/**
 * 创建API密钥
 * @param {string} accessToken 访问令牌
 * @param {number} workspaceId 工作区ID
 * @returns {Promise<string>} API密钥
 */
async function createApiKey(accessToken, workspaceId) {
    try {
        const response = await fetch("https://api.promptlayer.com/create-api-key", {
            method: "POST",
            headers: {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "authorization": `Bearer ${accessToken}`,
                "cache-control": "no-cache",
                "content-type": "application/json",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site",
                "Referer": "https://dashboard.promptlayer.com/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            body: JSON.stringify({ workspace_id: workspaceId }),
            dispatcher: proxyAgent
        });

        if (!response.ok) {
            throw new Error(`创建API密钥失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        if (!result.success || !result.api_key) {
            throw new Error('API密钥创建失败');
        }

        console.log(`成功创建API密钥: ${result.api_key}`);
        return result.api_key;
    } catch (error) {
        console.error(`创建API密钥时出错: ${error.message}`);
        throw error;
    }
}

/**
 * 注册PromptLayer账号的完整流程
 * @param {object} account 账号信息
 */
async function registerPromptLayer(account) {


    //proxyAgent = await getProxyAgent();

    let recaptcha_response;
    try
    {
        recaptcha_response = await getCaptchaRunToken(siteKey, siteReferer, siteAction, 180, 3)
    }
    catch(error){
        console.error(`验证码识别失败: ${error.message}`);
        return null;
    }

    try {
        const email = account.email;
        // 使用邮箱前缀作为用户名
        const name = email.split('@')[0];
        // 创建账号
        const accountResult = await createAccount(email, name,recaptcha_response);
        const accessToken = accountResult.access_token;

        // 获取工作区ID
        const workspaceId = await getWorkspaceId(accessToken);

        // 发送onboarding请求
        await sendOnboardingRequests(accessToken, name);

        // 创建API密钥
        const apiKey = await createApiKey(accessToken, workspaceId);

        // 保存账号信息
        await saveAccount(email, apiKey);

        return { email, apiKey };
    } catch (error) {
        //注册失败的账号..服务器会禁止再次注册
        console.error(`注册账号 ${account.email} 失败: ${error.message}`);
        // 保存错误账号到文件
        await saveErrorAccount(account.email);
        return null;
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        console.log('开始PromptLayer账号注册流程...');

        // 获取已注册的账号
        const registeredEmails = await getRegisteredEmails();
        // 获取注册失败的账号
        const errorEmails = await getErrorEmails();

        // 过滤出未注册的账号且不在错误邮箱列表中的账号
        const unregisteredAccounts = accounts.filter(account =>
            !registeredEmails.has(account.email) &&
            !errorEmails.includes(account.email)
        );
        console.log(`找到${unregisteredAccounts.length}个未注册的账号`);

        if (unregisteredAccounts.length === 0) {
            console.log('没有需要注册的账号');
            return;
        }

        // 注册账号
        const results = [];
        for (const account of unregisteredAccounts) {
            console.log(`正在注册账号: ${account.email}`);
            const result = await registerPromptLayer(account);
            if (result) {
                results.push(result);
            }
            // 添加延迟，避免请求过快
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log(`注册完成，成功注册${results.length}个账号`);
    } catch (error) {
        console.error(`执行过程中出错: ${error.message}`);
    }
}


// 执行主函数
main().catch(console.error);
